#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
向有效像元渔网提取降水数据工具（含分级功能）
读取现有的有效像元渔网，提取历史和未来降水值，生成新的渔网数据，并添加降水分级字段
"""

import os
import numpy as np
import rasterio
import geopandas as gpd
import pandas as pd
from rasterio.sample import sample_gen
import warnings
warnings.filterwarnings('ignore')

def check_and_create_output_dir(output_path):
    """检查并创建输出目录"""
    if not os.path.exists(output_path):
        os.makedirs(output_path, exist_ok=True)
        print(f"创建输出目录: {output_path}")
    else:
        print(f"输出目录已存在: {output_path}")

def classify_precipitation(precip_value):
    """
    降水分级函数
    1：0-25mm
    2：25-50mm
    3：50-100mm
    4：100-150mm
    5：≥150mm
    """
    if pd.isna(precip_value) or np.isnan(precip_value):
        return np.nan

    if precip_value < 25:
        return 1
    elif precip_value < 50:
        return 2
    elif precip_value < 100:
        return 3
    elif precip_value < 150:
        return 4
    else:
        return 5

def get_precipitation_class_description(class_value):
    """获取降水等级描述"""
    class_descriptions = {
        1: "0-25mm (极小)",
        2: "25-50mm (小)",
        3: "50-100mm (中等)",
        4: "100-150mm (大)",
        5: "≥150mm (极大)",
        np.nan: "无数据"
    }
    return class_descriptions.get(class_value, "未知")

def add_precipitation_classification(fishnet_gdf, precip_field, class_field):
    """添加降水分级字段"""
    print(f"  为 {precip_field} 添加分级字段 {class_field}...")
    
    # 应用分级函数
    fishnet_gdf[class_field] = fishnet_gdf[precip_field].apply(classify_precipitation)
    
    # 统计各等级数量
    class_counts = fishnet_gdf[class_field].value_counts().sort_index()
    print(f"  降水分级统计:")
    for class_val, count in class_counts.items():
        if not pd.isna(class_val):
            desc = get_precipitation_class_description(int(class_val))
            print(f"    等级 {int(class_val)}: {count} 个网格 ({desc})")
    
    # 统计无效值
    nan_count = fishnet_gdf[class_field].isna().sum()
    if nan_count > 0:
        print(f"    无效值: {nan_count} 个网格")
    
    return fishnet_gdf

def read_fishnet_shapefile(shapefile_path):
    """读取有效像元渔网shapefile"""
    try:
        fishnet_gdf = gpd.read_file(shapefile_path)
        print(f"成功读取渔网文件: {os.path.basename(shapefile_path)}")
        print(f"  - 网格数量: {len(fishnet_gdf)}")
        print(f"  - CRS: {fishnet_gdf.crs}")
        print(f"  - 现有字段: {list(fishnet_gdf.columns)}")
        
        # 显示边界范围
        bounds = fishnet_gdf.total_bounds
        print(f"  - 边界范围: [{bounds[0]:.6f}, {bounds[1]:.6f}, {bounds[2]:.6f}, {bounds[3]:.6f}]")
        
        return fishnet_gdf
    except Exception as e:
        print(f"读取渔网文件失败 {shapefile_path}: {e}")
        return None

def read_raster_data(file_path):
    """读取栅格数据"""
    try:
        with rasterio.open(file_path) as src:
            data = src.read(1)
            transform = src.transform
            crs = src.crs
            bounds = src.bounds
            nodata = src.nodata
            
        print(f"成功读取栅格: {os.path.basename(file_path)}")
        print(f"  - 数据形状: {data.shape}")
        print(f"  - CRS: {crs}")
        print(f"  - 数据范围: {np.nanmin(data):.2f} - {np.nanmax(data):.2f}")
        print(f"  - NoData值: {nodata}")
        
        return data, transform, crs, bounds, nodata
    except Exception as e:
        print(f"读取栅格文件失败 {file_path}: {e}")
        return None, None, None, None, None

def extract_raster_values_to_fishnet(fishnet_gdf, raster_file, field_name):
    """从栅格提取值到渔网"""
    print(f"\n提取 {field_name} 数据...")
    
    # 读取栅格数据
    data, transform, crs, bounds, nodata = read_raster_data(raster_file)
    if data is None:
        print(f"无法读取栅格文件: {raster_file}")
        return fishnet_gdf
    
    # 检查坐标系是否一致
    if fishnet_gdf.crs != crs:
        print(f"  坐标系不一致，将渔网从 {fishnet_gdf.crs} 转换为 {crs}")
        fishnet_gdf_proj = fishnet_gdf.to_crs(crs)
    else:
        fishnet_gdf_proj = fishnet_gdf.copy()
    
    # 获取网格中心点坐标
    centroids = fishnet_gdf_proj.geometry.centroid
    coords = [(point.x, point.y) for point in centroids]
    
    print(f"  提取 {len(coords)} 个点的栅格值...")
    
    # 使用rasterio的sample函数提取值
    try:
        with rasterio.open(raster_file) as src:
            # 提取栅格值
            sampled_values = list(sample_gen(src, coords))
            values = [val[0] if val.size > 0 else np.nan for val in sampled_values]
        
        # 处理NoData值
        if nodata is not None:
            values = [np.nan if val == nodata else val for val in values]
        
        # 添加到原始GeoDataFrame（保持原始坐标系）
        fishnet_gdf[field_name] = values
        
        # 统计信息
        valid_values = [v for v in values if not np.isnan(v)]
        print(f"  有效值数量: {len(valid_values)}/{len(values)}")
        if valid_values:
            print(f"  数据范围: {min(valid_values):.2f} - {max(valid_values):.2f}")
            print(f"  平均值: {np.mean(valid_values):.2f}")
        
        # 添加分级字段
        class_field = f"{field_name}_class"
        fishnet_gdf = add_precipitation_classification(fishnet_gdf, field_name, class_field)
        
    except Exception as e:
        print(f"  提取栅格值时出错: {e}")
        # 如果提取失败，添加空值列
        fishnet_gdf[field_name] = np.nan
        fishnet_gdf[f"{field_name}_class"] = np.nan
    
    return fishnet_gdf

def calculate_precipitation_changes(fishnet_gdf, hist_field, future_field):
    """计算降水变化量"""
    print(f"\n计算降水变化量...")
    
    # 计算绝对变化量
    change_field = f"{future_field}_minus_{hist_field}"
    fishnet_gdf[change_field] = fishnet_gdf[future_field] - fishnet_gdf[hist_field]
    
    # 计算相对变化百分比
    change_pct_field = f"{future_field}_pct_change"
    fishnet_gdf[change_pct_field] = ((fishnet_gdf[future_field] - fishnet_gdf[hist_field]) / 
                                    fishnet_gdf[hist_field] * 100)
    
    # 计算等级变化
    hist_class_field = f"{hist_field}_class"
    future_class_field = f"{future_field}_class"
    class_change_field = f"{future_field}_class_change"
    
    if hist_class_field in fishnet_gdf.columns and future_class_field in fishnet_gdf.columns:
        fishnet_gdf[class_change_field] = fishnet_gdf[future_class_field] - fishnet_gdf[hist_class_field]
        print(f"  添加等级变化字段: {class_change_field}")
        
        # 统计等级变化
        class_changes = fishnet_gdf[class_change_field].value_counts().sort_index()
        print(f"  等级变化统计:")
        for change_val, count in class_changes.items():
            if not pd.isna(change_val):
                if change_val > 0:
                    print(f"    升高 {int(change_val)} 级: {count} 个网格")
                elif change_val < 0:
                    print(f"    降低 {int(abs(change_val))} 级: {count} 个网格")
                else:
                    print(f"    无变化: {count} 个网格")
    
    # 统计变化量
    valid_changes = fishnet_gdf[change_field].dropna()
    valid_pct_changes = fishnet_gdf[change_pct_field].dropna()
    
    if len(valid_changes) > 0:
        print(f"  绝对变化量统计:")
        print(f"    平均变化: {valid_changes.mean():.2f} mm")
        print(f"    变化范围: {valid_changes.min():.2f} - {valid_changes.max():.2f} mm")
        print(f"    标准差: {valid_changes.std():.2f} mm")
    
    if len(valid_pct_changes) > 0:
        print(f"  相对变化量统计:")
        print(f"    平均变化: {valid_pct_changes.mean():.2f}%")
        print(f"    变化范围: {valid_pct_changes.min():.2f}% - {valid_pct_changes.max():.2f}%")
        print(f"    标准差: {valid_pct_changes.std():.2f}%")
    
    return fishnet_gdf

def export_enhanced_fishnet(fishnet_gdf, output_dir, base_name="precipitation_fishnet_classified"):
    """导出增强的渔网数据"""
    print(f"\n导出增强渔网数据到: {output_dir}")

    # 导出Shapefile
    shapefile_path = os.path.join(output_dir, f"{base_name}.shp")
    try:
        fishnet_gdf.to_file(shapefile_path, encoding='utf-8')
        print(f"✓ Shapefile已保存: {shapefile_path}")
    except Exception as e:
        print(f"✗ Shapefile保存失败: {e}")

    # 导出GeoJSON
    geojson_path = os.path.join(output_dir, f"{base_name}.geojson")
    try:
        fishnet_gdf.to_file(geojson_path, driver='GeoJSON', encoding='utf-8')
        print(f"✓ GeoJSON已保存: {geojson_path}")
    except Exception as e:
        print(f"✗ GeoJSON保存失败: {e}")

    # 导出CSV（不包含几何信息）
    csv_path = os.path.join(output_dir, f"{base_name}_data.csv")
    try:
        data_df = fishnet_gdf.drop('geometry', axis=1)
        data_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"✓ CSV数据已保存: {csv_path}")
    except Exception as e:
        print(f"✗ CSV保存失败: {e}")

    # 导出Excel
    excel_path = os.path.join(output_dir, f"{base_name}_data.xlsx")
    try:
        data_df = fishnet_gdf.drop('geometry', axis=1)
        data_df.to_excel(excel_path, index=False, engine='openpyxl')
        print(f"✓ Excel数据已保存: {excel_path}")
    except Exception as e:
        print(f"✗ Excel保存失败: {e}")

    # 生成详细的字段说明文件
    readme_path = os.path.join(output_dir, f"{base_name}_fields_description.txt")
    try:
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write("降水数据渔网字段说明（含分级）\n")
            f.write("=" * 60 + "\n\n")

            f.write("原始字段:\n")
            original_fields = ['grid_id', 'row_index', 'col_index', 'pixel_value',
                             'center_x', 'center_y', 'x_min', 'x_max', 'y_min', 'y_max', 'area_deg2']
            for field in original_fields:
                if field in fishnet_gdf.columns:
                    f.write(f"- {field}: 原始渔网字段\n")

            f.write("\n降水数据字段:\n")
            for col in fishnet_gdf.columns:
                if 'historical_precip' == col:
                    f.write(f"- {col}: 历史极端降水值 (mm)\n")
                elif 'future_precip' == col:
                    f.write(f"- {col}: 未来极端降水值 (mm)\n")
                elif col.endswith('_class') and 'change' not in col:
                    f.write(f"- {col}: 降水分级 (1-5级)\n")
                elif 'minus' in col.lower():
                    f.write(f"- {col}: 降水绝对变化量 (mm)\n")
                elif 'pct_change' in col.lower():
                    f.write(f"- {col}: 降水相对变化百分比 (%)\n")
                elif 'class_change' in col.lower():
                    f.write(f"- {col}: 降水等级变化 (级数差)\n")

            f.write(f"\n降水分级标准:\n")
            f.write(f"- 1级: 0-25mm (极小)\n")
            f.write(f"- 2级: 25-50mm (小)\n")
            f.write(f"- 3级: 50-100mm (中等)\n")
            f.write(f"- 4级: 100-150mm (大)\n")
            f.write(f"- 5级: ≥150mm (极大)\n")

            f.write(f"\n数据统计:\n")
            f.write(f"- 总网格数: {len(fishnet_gdf)}\n")
            f.write(f"- 坐标系: {fishnet_gdf.crs}\n")

            # 添加分级统计
            for col in fishnet_gdf.columns:
                if col.endswith('_class') and 'change' not in col:
                    f.write(f"\n{col} 分级统计:\n")
                    class_counts = fishnet_gdf[col].value_counts().sort_index()
                    for class_val, count in class_counts.items():
                        if not pd.isna(class_val):
                            desc = get_precipitation_class_description(int(class_val))
                            f.write(f"  等级 {int(class_val)}: {count} 个网格 ({desc})\n")

        print(f"✓ 字段说明已保存: {readme_path}")
    except Exception as e:
        print(f"✗ 字段说明保存失败: {e}")

def main():
    """主函数"""
    print("开始处理降水数据提取到渔网（含分级功能）...")

    # 文件路径设置
    fishnet_file = r"Z:\yuan\paper3_new02\shp\basins_fishnet\valid_pixels_fishnet.shp"
    historical_file = r"Z:\yuan\paper3_new02\EPEs_clip\historical_mean\RX1-day_1971_2020_mean.tif"
    future_file = r"Z:\yuan\paper3_new02\EPEs_clip\ssp_mme_jz_5yr\ssp245\Mean\RX1-day\RX1-day_2100.tif"
    output_dir = r"Z:\yuan\paper3_new02\EPEs_plot\EPEs_his_map7"

    # 检查并创建输出目录
    check_and_create_output_dir(output_dir)

    # 读取有效像元渔网
    print("\n1. 读取有效像元渔网...")
    fishnet_gdf = read_fishnet_shapefile(fishnet_file)
    if fishnet_gdf is None:
        print("渔网文件读取失败，程序退出")
        return

    # 创建渔网副本，避免修改原始数据
    enhanced_fishnet = fishnet_gdf.copy()

    # 提取历史降水数据（含分级）
    print("\n2. 提取历史降水数据（含分级）...")
    enhanced_fishnet = extract_raster_values_to_fishnet(
        enhanced_fishnet, historical_file, 'historical_precip'
    )

    # 提取未来降水数据（含分级）
    print("\n3. 提取未来降水数据（含分级）...")
    enhanced_fishnet = extract_raster_values_to_fishnet(
        enhanced_fishnet, future_file, 'future_precip'
    )

    # 计算降水变化量（含等级变化）
    print("\n4. 计算降水变化量（含等级变化）...")
    enhanced_fishnet = calculate_precipitation_changes(
        enhanced_fishnet, 'historical_precip', 'future_precip'
    )

    # 导出增强的渔网数据
    print("\n5. 导出增强渔网数据...")
    export_enhanced_fishnet(enhanced_fishnet, output_dir, "precipitation_classified_fishnet")

    print(f"\n✓ 处理完成！")
    print(f"✓ 原始渔网保持不变")
    print(f"✓ 增强渔网已保存到: {output_dir}")
    print(f"✓ 新增字段包括:")
    print(f"  - historical_precip, historical_precip_class")
    print(f"  - future_precip, future_precip_class")
    print(f"  - future_precip_minus_historical_precip")
    print(f"  - future_precip_pct_change")
    print(f"  - future_precip_class_change")
    print(f"✓ 降水分级: 1(0-25mm), 2(25-50mm), 3(50-100mm), 4(100-150mm), 5(≥150mm)")

if __name__ == "__main__":
    main()
