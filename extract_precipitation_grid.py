#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
极端降水数据网格提取工具
读取历史和未来极端降水数据，生成方格渔网并提取降水值
"""

import os
import numpy as np
import rasterio
import geopandas as gpd
from shapely.geometry import Polygon, box
import pandas as pd
from rasterio.features import shapes
from rasterio.transform import from_bounds
import warnings
warnings.filterwarnings('ignore')

def check_and_create_output_dir(output_path):
    """检查并创建输出目录"""
    if not os.path.exists(output_path):
        os.makedirs(output_path, exist_ok=True)
        print(f"创建输出目录: {output_path}")
    else:
        print(f"输出目录已存在: {output_path}")

def read_raster_data(file_path):
    """读取栅格数据"""
    try:
        with rasterio.open(file_path) as src:
            data = src.read(1)
            transform = src.transform
            crs = src.crs
            bounds = src.bounds
            nodata = src.nodata
        print(f"成功读取: {os.path.basename(file_path)}")
        print(f"  - 数据形状: {data.shape}")
        print(f"  - CRS: {crs}")
        print(f"  - 数据范围: {np.nanmin(data):.2f} - {np.nanmax(data):.2f}")
        return data, transform, crs, bounds, nodata
    except Exception as e:
        print(f"读取文件失败 {file_path}: {e}")
        return None, None, None, None, None

def create_valid_pixels_mask(valid_pixels_file, historical_data, future_data, nodata_value):
    """创建有效像元掩膜"""
    print("创建有效像元掩膜...")
    
    # 读取有效像元文件
    if os.path.exists(valid_pixels_file):
        valid_data, _, _, _, _ = read_raster_data(valid_pixels_file)
        if valid_data is not None:
            # 使用有效像元文件作为掩膜
            mask = (valid_data != nodata_value) & (~np.isnan(valid_data))
        else:
            # 如果读取失败，使用历史和未来数据的交集
            mask = (~np.isnan(historical_data)) & (~np.isnan(future_data))
            if nodata_value is not None:
                mask = mask & (historical_data != nodata_value) & (future_data != nodata_value)
    else:
        print(f"有效像元文件不存在: {valid_pixels_file}")
        # 使用历史和未来数据的交集
        mask = (~np.isnan(historical_data)) & (~np.isnan(future_data))
        if nodata_value is not None:
            mask = mask & (historical_data != nodata_value) & (future_data != nodata_value)
    
    print(f"有效像元数量: {np.sum(mask)}")
    return mask

def create_fishnet_grid(bounds, transform, mask, crs):
    """根据有效像元创建方格渔网"""
    print("创建方格渔网...")
    
    # 获取栅格的像元大小
    pixel_width = abs(transform[0])
    pixel_height = abs(transform[4])
    
    # 获取有效像元的行列索引
    rows, cols = np.where(mask)
    
    polygons = []
    row_indices = []
    col_indices = []
    
    for i, (row, col) in enumerate(zip(rows, cols)):
        # 计算像元的地理坐标
        x_min = bounds.left + col * pixel_width
        x_max = x_min + pixel_width
        y_max = bounds.top - row * pixel_height
        y_min = y_max - pixel_height
        
        # 创建多边形
        polygon = box(x_min, y_min, x_max, y_max)
        polygons.append(polygon)
        row_indices.append(row)
        col_indices.append(col)
    
    # 创建GeoDataFrame
    gdf = gpd.GeoDataFrame({
        'grid_id': range(len(polygons)),
        'row': row_indices,
        'col': col_indices,
        'geometry': polygons
    }, crs=crs)
    
    print(f"创建了 {len(gdf)} 个网格单元")
    return gdf

def extract_values_to_grid(grid_gdf, data_array, transform, data_name):
    """提取栅格值到网格"""
    print(f"提取 {data_name} 数据到网格...")
    
    values = []
    for _, row in grid_gdf.iterrows():
        grid_row = row['row']
        grid_col = row['col']
        
        # 提取对应位置的值
        if 0 <= grid_row < data_array.shape[0] and 0 <= grid_col < data_array.shape[1]:
            value = data_array[grid_row, grid_col]
            values.append(value)
        else:
            values.append(np.nan)
    
    # 添加到GeoDataFrame
    grid_gdf[data_name] = values
    print(f"  - 有效值数量: {np.sum(~np.isnan(values))}")
    print(f"  - 数据范围: {np.nanmin(values):.2f} - {np.nanmax(values):.2f}")
    
    return grid_gdf

def main():
    """主函数"""
    print("开始处理极端降水数据...")
    
    # 文件路径设置
    historical_file = r"Z:\yuan\paper3_new02\EPEs_clip\historical_mean\RX1-day_1971_2020_mean.tif"
    future_file = r"Z:\yuan\paper3_new02\EPEs_clip\ssp_mme_jz_5yr\ssp245\Mean\RX1-day\RX1-day_2100.tif"
    valid_pixels_file = r"Z:\yuan\clip_biaozhun_tif\clip_tif_1_basin4_600_1440.tif"
    output_dir = r"Z:\yuan\paper3_new02\EPEs_plot\EPEs_his_map2"
    
    # 检查并创建输出目录
    check_and_create_output_dir(output_dir)
    
    # 读取历史数据
    print("\n1. 读取历史极端降水数据...")
    hist_data, hist_transform, hist_crs, hist_bounds, hist_nodata = read_raster_data(historical_file)
    if hist_data is None:
        print("历史数据读取失败，程序退出")
        return
    
    # 读取未来数据
    print("\n2. 读取未来极端降水数据...")
    future_data, future_transform, future_crs, future_bounds, future_nodata = read_raster_data(future_file)
    if future_data is None:
        print("未来数据读取失败，程序退出")
        return
    
    # 检查数据一致性
    if hist_data.shape != future_data.shape:
        print(f"警告: 历史数据形状 {hist_data.shape} 与未来数据形状 {future_data.shape} 不一致")
        return
    
    if hist_crs != future_crs:
        print(f"警告: 历史数据CRS {hist_crs} 与未来数据CRS {future_crs} 不一致")
        return
    
    # 创建有效像元掩膜
    print("\n3. 创建有效像元掩膜...")
    valid_mask = create_valid_pixels_mask(valid_pixels_file, hist_data, future_data, hist_nodata)
    
    # 创建方格渔网
    print("\n4. 创建方格渔网...")
    fishnet_gdf = create_fishnet_grid(hist_bounds, hist_transform, valid_mask, hist_crs)
    
    # 提取历史降水值
    print("\n5. 提取历史降水值...")
    fishnet_gdf = extract_values_to_grid(fishnet_gdf, hist_data, hist_transform, 'historical_precip')
    
    # 提取未来降水值
    print("\n6. 提取未来降水值...")
    fishnet_gdf = extract_values_to_grid(fishnet_gdf, future_data, future_transform, 'future_precip')
    
    # 计算变化量
    print("\n7. 计算降水变化量...")
    fishnet_gdf['precip_change'] = fishnet_gdf['future_precip'] - fishnet_gdf['historical_precip']
    fishnet_gdf['precip_change_pct'] = ((fishnet_gdf['future_precip'] - fishnet_gdf['historical_precip']) / 
                                       fishnet_gdf['historical_precip'] * 100)
    
    # 输出统计信息
    print("\n8. 数据统计信息:")
    print(f"历史降水平均值: {fishnet_gdf['historical_precip'].mean():.2f} mm")
    print(f"未来降水平均值: {fishnet_gdf['future_precip'].mean():.2f} mm")
    print(f"平均变化量: {fishnet_gdf['precip_change'].mean():.2f} mm")
    print(f"平均变化百分比: {fishnet_gdf['precip_change_pct'].mean():.2f}%")
    
    # 导出方格渔网shapefile
    print("\n9. 导出方格渔网shapefile...")
    output_shapefile = os.path.join(output_dir, "precipitation_fishnet_grid.shp")
    fishnet_gdf.to_file(output_shapefile, encoding='utf-8')
    print(f"方格渔网已保存: {output_shapefile}")
    
    # 导出CSV文件
    print("\n10. 导出CSV数据...")
    output_csv = os.path.join(output_dir, "precipitation_data.csv")
    # 移除geometry列用于CSV导出
    data_df = fishnet_gdf.drop('geometry', axis=1)
    data_df.to_csv(output_csv, index=False, encoding='utf-8-sig')
    print(f"数据表已保存: {output_csv}")
    
    print(f"\n处理完成！所有文件已保存到: {output_dir}")

if __name__ == "__main__":
    main()
