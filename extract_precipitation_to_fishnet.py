#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
向有效像元渔网提取降水数据工具
读取现有的有效像元渔网，提取历史和未来降水值，生成新的渔网数据
"""

import os
import numpy as np
import rasterio
import geopandas as gpd
import pandas as pd
from rasterio.sample import sample_gen
import warnings
warnings.filterwarnings('ignore')

def check_and_create_output_dir(output_path):
    """检查并创建输出目录"""
    if not os.path.exists(output_path):
        os.makedirs(output_path, exist_ok=True)
        print(f"创建输出目录: {output_path}")
    else:
        print(f"输出目录已存在: {output_path}")

def read_fishnet_shapefile(shapefile_path):
    """读取有效像元渔网shapefile"""
    try:
        fishnet_gdf = gpd.read_file(shapefile_path)
        print(f"成功读取渔网文件: {os.path.basename(shapefile_path)}")
        print(f"  - 网格数量: {len(fishnet_gdf)}")
        print(f"  - CRS: {fishnet_gdf.crs}")
        print(f"  - 现有字段: {list(fishnet_gdf.columns)}")
        
        # 显示边界范围
        bounds = fishnet_gdf.total_bounds
        print(f"  - 边界范围: [{bounds[0]:.6f}, {bounds[1]:.6f}, {bounds[2]:.6f}, {bounds[3]:.6f}]")
        
        return fishnet_gdf
    except Exception as e:
        print(f"读取渔网文件失败 {shapefile_path}: {e}")
        return None

def read_raster_data(file_path):
    """读取栅格数据"""
    try:
        with rasterio.open(file_path) as src:
            data = src.read(1)
            transform = src.transform
            crs = src.crs
            bounds = src.bounds
            nodata = src.nodata
            
        print(f"成功读取栅格: {os.path.basename(file_path)}")
        print(f"  - 数据形状: {data.shape}")
        print(f"  - CRS: {crs}")
        print(f"  - 数据范围: {np.nanmin(data):.2f} - {np.nanmax(data):.2f}")
        print(f"  - NoData值: {nodata}")
        
        return data, transform, crs, bounds, nodata
    except Exception as e:
        print(f"读取栅格文件失败 {file_path}: {e}")
        return None, None, None, None, None

def extract_raster_values_to_fishnet(fishnet_gdf, raster_file, field_name):
    """从栅格提取值到渔网"""
    print(f"\n提取 {field_name} 数据...")
    
    # 读取栅格数据
    data, transform, crs, bounds, nodata = read_raster_data(raster_file)
    if data is None:
        print(f"无法读取栅格文件: {raster_file}")
        return fishnet_gdf
    
    # 检查坐标系是否一致
    if fishnet_gdf.crs != crs:
        print(f"  坐标系不一致，将渔网从 {fishnet_gdf.crs} 转换为 {crs}")
        fishnet_gdf_proj = fishnet_gdf.to_crs(crs)
    else:
        fishnet_gdf_proj = fishnet_gdf.copy()
    
    # 获取网格中心点坐标
    centroids = fishnet_gdf_proj.geometry.centroid
    coords = [(point.x, point.y) for point in centroids]
    
    print(f"  提取 {len(coords)} 个点的栅格值...")
    
    # 使用rasterio的sample函数提取值
    try:
        with rasterio.open(raster_file) as src:
            # 提取栅格值
            sampled_values = list(sample_gen(src, coords))
            values = [val[0] if val.size > 0 else np.nan for val in sampled_values]
        
        # 处理NoData值
        if nodata is not None:
            values = [np.nan if val == nodata else val for val in values]
        
        # 添加到原始GeoDataFrame（保持原始坐标系）
        fishnet_gdf[field_name] = values
        
        # 统计信息
        valid_values = [v for v in values if not np.isnan(v)]
        print(f"  有效值数量: {len(valid_values)}/{len(values)}")
        if valid_values:
            print(f"  数据范围: {min(valid_values):.2f} - {max(valid_values):.2f}")
            print(f"  平均值: {np.mean(valid_values):.2f}")
        
    except Exception as e:
        print(f"  提取栅格值时出错: {e}")
        # 如果提取失败，添加空值列
        fishnet_gdf[field_name] = np.nan
    
    return fishnet_gdf

def calculate_precipitation_changes(fishnet_gdf, hist_field, future_field):
    """计算降水变化量"""
    print(f"\n计算降水变化量...")
    
    # 计算绝对变化量
    change_field = f"{future_field}_minus_{hist_field}"
    fishnet_gdf[change_field] = fishnet_gdf[future_field] - fishnet_gdf[hist_field]
    
    # 计算相对变化百分比
    change_pct_field = f"{future_field}_pct_change"
    fishnet_gdf[change_pct_field] = ((fishnet_gdf[future_field] - fishnet_gdf[hist_field]) / 
                                    fishnet_gdf[hist_field] * 100)
    
    # 统计变化量
    valid_changes = fishnet_gdf[change_field].dropna()
    valid_pct_changes = fishnet_gdf[change_pct_field].dropna()
    
    if len(valid_changes) > 0:
        print(f"  绝对变化量统计:")
        print(f"    平均变化: {valid_changes.mean():.2f} mm")
        print(f"    变化范围: {valid_changes.min():.2f} - {valid_changes.max():.2f} mm")
        print(f"    标准差: {valid_changes.std():.2f} mm")
    
    if len(valid_pct_changes) > 0:
        print(f"  相对变化量统计:")
        print(f"    平均变化: {valid_pct_changes.mean():.2f}%")
        print(f"    变化范围: {valid_pct_changes.min():.2f}% - {valid_pct_changes.max():.2f}%")
        print(f"    标准差: {valid_pct_changes.std():.2f}%")
    
    return fishnet_gdf

def export_enhanced_fishnet(fishnet_gdf, output_dir, base_name="precipitation_fishnet"):
    """导出增强的渔网数据"""
    print(f"\n导出增强渔网数据到: {output_dir}")
    
    # 导出Shapefile
    shapefile_path = os.path.join(output_dir, f"{base_name}.shp")
    try:
        fishnet_gdf.to_file(shapefile_path, encoding='utf-8')
        print(f"✓ Shapefile已保存: {shapefile_path}")
    except Exception as e:
        print(f"✗ Shapefile保存失败: {e}")
    
    # 导出GeoJSON
    geojson_path = os.path.join(output_dir, f"{base_name}.geojson")
    try:
        fishnet_gdf.to_file(geojson_path, driver='GeoJSON', encoding='utf-8')
        print(f"✓ GeoJSON已保存: {geojson_path}")
    except Exception as e:
        print(f"✗ GeoJSON保存失败: {e}")
    
    # 导出CSV（不包含几何信息）
    csv_path = os.path.join(output_dir, f"{base_name}_data.csv")
    try:
        data_df = fishnet_gdf.drop('geometry', axis=1)
        data_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"✓ CSV数据已保存: {csv_path}")
    except Exception as e:
        print(f"✗ CSV保存失败: {e}")
    
    # 导出Excel
    excel_path = os.path.join(output_dir, f"{base_name}_data.xlsx")
    try:
        data_df = fishnet_gdf.drop('geometry', axis=1)
        data_df.to_excel(excel_path, index=False, engine='openpyxl')
        print(f"✓ Excel数据已保存: {excel_path}")
    except Exception as e:
        print(f"✗ Excel保存失败: {e}")
    
    # 生成字段说明文件
    readme_path = os.path.join(output_dir, f"{base_name}_fields_description.txt")
    try:
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write("降水数据渔网字段说明\n")
            f.write("=" * 50 + "\n\n")
            f.write("原始字段:\n")
            original_fields = ['grid_id', 'row_index', 'col_index', 'pixel_value', 
                             'center_x', 'center_y', 'x_min', 'x_max', 'y_min', 'y_max', 'area_deg2']
            for field in original_fields:
                if field in fishnet_gdf.columns:
                    f.write(f"- {field}: 原始渔网字段\n")
            
            f.write("\n新增降水字段:\n")
            for col in fishnet_gdf.columns:
                if 'historical' in col.lower():
                    f.write(f"- {col}: 历史极端降水值 (mm)\n")
                elif 'future' in col.lower() and 'pct' not in col.lower() and 'minus' not in col.lower():
                    f.write(f"- {col}: 未来极端降水值 (mm)\n")
                elif 'minus' in col.lower():
                    f.write(f"- {col}: 降水绝对变化量 (mm)\n")
                elif 'pct_change' in col.lower():
                    f.write(f"- {col}: 降水相对变化百分比 (%)\n")
            
            f.write(f"\n数据统计:\n")
            f.write(f"- 总网格数: {len(fishnet_gdf)}\n")
            f.write(f"- 坐标系: {fishnet_gdf.crs}\n")
            
        print(f"✓ 字段说明已保存: {readme_path}")
    except Exception as e:
        print(f"✗ 字段说明保存失败: {e}")

def main():
    """主函数"""
    print("开始处理降水数据提取到渔网...")
    
    # 文件路径设置
    fishnet_file = r"Z:\yuan\paper3_new02\shp\basins_fishnet\valid_pixels_fishnet.shp"
    historical_file = r"Z:\yuan\paper3_new02\EPEs_clip\historical_mean\RX1-day_1971_2020_mean.tif"
    future_file = r"Z:\yuan\paper3_new02\EPEs_clip\ssp_mme_jz_5yr\ssp245\Mean\RX1-day\RX1-day_2100.tif"
    output_dir = r"Z:\yuan\paper3_new02\EPEs_plot\EPEs_his_map3"
    
    # 检查并创建输出目录
    check_and_create_output_dir(output_dir)
    
    # 读取有效像元渔网
    print("\n1. 读取有效像元渔网...")
    fishnet_gdf = read_fishnet_shapefile(fishnet_file)
    if fishnet_gdf is None:
        print("渔网文件读取失败，程序退出")
        return
    
    # 创建渔网副本，避免修改原始数据
    enhanced_fishnet = fishnet_gdf.copy()
    
    # 提取历史降水数据
    print("\n2. 提取历史降水数据...")
    enhanced_fishnet = extract_raster_values_to_fishnet(
        enhanced_fishnet, historical_file, 'historical_precip'
    )
    
    # 提取未来降水数据
    print("\n3. 提取未来降水数据...")
    enhanced_fishnet = extract_raster_values_to_fishnet(
        enhanced_fishnet, future_file, 'future_precip'
    )
    
    # 计算降水变化量
    print("\n4. 计算降水变化量...")
    enhanced_fishnet = calculate_precipitation_changes(
        enhanced_fishnet, 'historical_precip', 'future_precip'
    )
    
    # 导出增强的渔网数据
    print("\n5. 导出增强渔网数据...")
    export_enhanced_fishnet(enhanced_fishnet, output_dir, "precipitation_enhanced_fishnet")
    
    print(f"\n✓ 处理完成！")
    print(f"✓ 原始渔网保持不变")
    print(f"✓ 增强渔网已保存到: {output_dir}")
    print(f"✓ 新增字段: historical_precip, future_precip, future_precip_minus_historical_precip, future_precip_pct_change")

if __name__ == "__main__":
    main()
