#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于有效像元掩膜更新流域ID文件工具
读取流域ID文件和有效像元文件，将不在有效像元范围内的流域ID设为NoData
"""

import os
import numpy as np
import rasterio
import warnings
warnings.filterwarnings('ignore')

def check_and_create_output_dir(output_path):
    """检查并创建输出目录"""
    if not os.path.exists(output_path):
        os.makedirs(output_path, exist_ok=True)
        print(f"创建输出目录: {output_path}")
    else:
        print(f"输出目录已存在: {output_path}")

def read_raster_with_info(file_path, data_name):
    """读取栅格数据并显示详细信息"""
    try:
        with rasterio.open(file_path) as src:
            data = src.read(1)
            profile = src.profile
            transform = src.transform
            crs = src.crs
            bounds = src.bounds
            nodata = src.nodata
            
        print(f"\n成功读取{data_name}: {os.path.basename(file_path)}")
        print(f"  - 数据形状: {data.shape}")
        print(f"  - 数据类型: {data.dtype}")
        print(f"  - CRS: {crs}")
        print(f"  - NoData值: {nodata}")
        print(f"  - 边界范围: {bounds}")
        print(f"  - 像元大小: {abs(transform[0]):.6f} × {abs(transform[4]):.6f}")
        
        # 统计有效值
        if nodata is not None:
            valid_mask = (data != nodata) & (~np.isnan(data))
        else:
            valid_mask = ~np.isnan(data)
            
        valid_count = np.sum(valid_mask)
        total_count = data.size
        
        print(f"  - 总像元数: {total_count}")
        print(f"  - 有效像元数: {valid_count}")
        print(f"  - 有效比例: {valid_count/total_count*100:.2f}%")
        
        if valid_count > 0:
            valid_data = data[valid_mask]
            unique_values = np.unique(valid_data)
            print(f"  - 唯一值数量: {len(unique_values)}")
            print(f"  - 数据范围: {np.min(valid_data):.2f} - {np.max(valid_data):.2f}")
            
            # 如果是流域ID，显示流域ID信息
            if "流域" in data_name or "basin" in data_name.lower():
                if len(unique_values) <= 50:
                    print(f"  - 流域ID列表: {unique_values}")
                else:
                    print(f"  - 前20个流域ID: {unique_values[:20]}")
                    print(f"  - 后20个流域ID: {unique_values[-20:]}")
        
        return data, profile, transform, crs, bounds, nodata
        
    except Exception as e:
        print(f"读取{data_name}失败 {file_path}: {e}")
        return None, None, None, None, None, None

def check_raster_compatibility(basin_data, basin_profile, valid_data, valid_profile):
    """检查两个栅格的兼容性"""
    print("\n检查栅格兼容性...")
    
    # 检查数据形状
    if basin_data.shape != valid_data.shape:
        print(f"✗ 数据形状不匹配:")
        print(f"  流域ID文件: {basin_data.shape}")
        print(f"  有效像元文件: {valid_data.shape}")
        return False
    
    # 检查坐标系
    if basin_profile['crs'] != valid_profile['crs']:
        print(f"✗ 坐标系不匹配:")
        print(f"  流域ID文件: {basin_profile['crs']}")
        print(f"  有效像元文件: {valid_profile['crs']}")
        return False
    
    # 检查变换矩阵（允许小的数值误差）
    basin_transform = basin_profile['transform']
    valid_transform = valid_profile['transform']
    
    transform_diff = np.array(basin_transform) - np.array(valid_transform)
    max_diff = np.max(np.abs(transform_diff))
    
    if max_diff > 1e-10:
        print(f"✗ 变换矩阵不匹配 (最大差异: {max_diff}):")
        print(f"  流域ID文件: {basin_transform}")
        print(f"  有效像元文件: {valid_transform}")
        return False
    
    print("✓ 栅格兼容性检查通过")
    return True

def create_masked_basin_id(basin_data, basin_nodata, valid_data, valid_nodata):
    """根据有效像元掩膜创建新的流域ID文件，返回(新数据, 使用的NoData值)"""
    print("\n根据有效像元掩膜创建新的流域ID文件...")
    
    # 创建有效像元掩膜
    if valid_nodata is not None:
        valid_mask = (valid_data != valid_nodata) & (~np.isnan(valid_data))
    else:
        valid_mask = ~np.isnan(valid_data)
    
    print(f"有效像元掩膜统计:")
    print(f"  有效像元数量: {np.sum(valid_mask)}")
    print(f"  无效像元数量: {np.sum(~valid_mask)}")
    
    # 创建流域ID的有效掩膜（排除NoData值和0值）
    if basin_nodata is not None:
        basin_valid_mask = (basin_data != basin_nodata) & (~np.isnan(basin_data)) & (basin_data != 0)
    else:
        basin_valid_mask = (~np.isnan(basin_data)) & (basin_data != 0)
    
    print(f"原始流域ID统计:")
    print(f"  原始有效流域ID像元数: {np.sum(basin_valid_mask)}")
    
    # 复制流域ID数据
    new_basin_data = basin_data.copy()
    
    # 统计处理前的流域ID
    if np.sum(basin_valid_mask) > 0:
        original_basin_ids = np.unique(basin_data[basin_valid_mask])
        print(f"  原始流域ID数量: {len(original_basin_ids)}")
    
    # 将不在有效像元范围内的流域ID设为NoData
    invalid_positions = ~valid_mask
    masked_count = 0

    # 遍历所有位置，将无效位置的流域ID设为NoData
    if basin_nodata is not None:
        # 统计将被掩膜的有效流域ID像元
        will_be_masked = basin_valid_mask & invalid_positions
        masked_count = np.sum(will_be_masked)

        # 应用掩膜
        new_basin_data[invalid_positions] = basin_nodata
    else:
        # 如果原始文件没有NoData值，设置一个合适的NoData值而不是使用NaN
        will_be_masked = basin_valid_mask & invalid_positions
        masked_count = np.sum(will_be_masked)

        # 保持原始数据类型，使用一个不会与有效流域ID冲突的值作为NoData
        # 对于uint32类型，使用最大值作为NoData
        if new_basin_data.dtype == np.uint32:
            nodata_value = np.iinfo(np.uint32).max  # 4294967295
        elif new_basin_data.dtype == np.int32:
            nodata_value = np.iinfo(np.int32).min   # -2147483648
        else:
            # 对于其他整数类型，转换为float64以保持精度
            new_basin_data = new_basin_data.astype(np.float64)
            nodata_value = np.nan

        # 应用掩膜
        new_basin_data[invalid_positions] = nodata_value
        # 更新basin_nodata为实际使用的值
        used_nodata = nodata_value

    if basin_nodata is not None:
        used_nodata = basin_nodata

    # 统计处理后的结果（排除NoData值和0值）
    if not np.isnan(used_nodata):
        new_basin_valid_mask = (new_basin_data != used_nodata) & (~np.isnan(new_basin_data)) & (new_basin_data != 0)
    else:
        new_basin_valid_mask = (~np.isnan(new_basin_data)) & (new_basin_data != 0)
    
    new_valid_count = np.sum(new_basin_valid_mask)
    
    print(f"\n掩膜处理结果:")
    print(f"  被掩膜的流域ID像元数: {masked_count}")
    print(f"  保留的有效流域ID像元数: {new_valid_count}")
    print(f"  保留比例: {new_valid_count/np.sum(basin_valid_mask)*100:.2f}%")
    
    # 统计保留的流域ID
    if new_valid_count > 0:
        remaining_basin_ids = np.unique(new_basin_data[new_basin_valid_mask])
        print(f"  保留的流域ID数量: {len(remaining_basin_ids)}")
        
        if len(remaining_basin_ids) <= 50:
            print(f"  保留的流域ID列表: {remaining_basin_ids}")
        else:
            print(f"  前20个保留的流域ID: {remaining_basin_ids[:20]}")
            print(f"  后20个保留的流域ID: {remaining_basin_ids[-20:]}")

    return new_basin_data, used_nodata

def save_masked_basin_id(data, profile, output_path, filename, custom_nodata=None):
    """保存掩膜后的流域ID文件"""
    print(f"\n保存掩膜后的流域ID文件...")

    output_file = os.path.join(output_path, filename)

    try:
        # 更新profile
        updated_profile = profile.copy()
        updated_profile['dtype'] = data.dtype

        # 设置正确的NoData值
        if custom_nodata is not None:
            updated_profile['nodata'] = custom_nodata
            print(f"  使用自定义NoData值: {custom_nodata}")
        elif profile['nodata'] is not None:
            # 保持原有的NoData值，但确保类型匹配
            if data.dtype == np.float64:
                updated_profile['nodata'] = float(profile['nodata'])
            else:
                updated_profile['nodata'] = profile['nodata']
        else:
            # 根据数据类型设置合适的NoData值
            if data.dtype == np.uint32:
                updated_profile['nodata'] = np.iinfo(np.uint32).max
            elif data.dtype == np.int32:
                updated_profile['nodata'] = np.iinfo(np.int32).min
            elif data.dtype == np.float64:
                updated_profile['nodata'] = np.nan

        print(f"  保存数据类型: {data.dtype}")
        print(f"  保存NoData值: {updated_profile['nodata']}")

        with rasterio.open(output_file, 'w', **updated_profile) as dst:
            dst.write(data, 1)
        
        print(f"✓ 掩膜后的流域ID文件已保存: {output_file}")
        
        # 验证保存的文件
        with rasterio.open(output_file) as src:
            saved_data = src.read(1)
            saved_nodata = src.nodata
            
        if saved_nodata is not None:
            saved_valid_mask = (saved_data != saved_nodata) & (~np.isnan(saved_data)) & (saved_data != 0)
        else:
            saved_valid_mask = (~np.isnan(saved_data)) & (saved_data != 0)
            
        saved_valid_count = np.sum(saved_valid_mask)
        print(f"✓ 验证保存结果: {saved_valid_count} 个有效流域ID像元")
        
        if saved_valid_count > 0:
            saved_unique_ids = np.unique(saved_data[saved_valid_mask])
            print(f"✓ 验证保存结果: {len(saved_unique_ids)} 个唯一流域ID")
        
        return output_file
        
    except Exception as e:
        print(f"✗ 保存流域ID文件失败: {e}")
        return None

def generate_processing_summary(output_path, original_valid_count, original_basin_count, 
                              new_valid_count, new_basin_count, masked_count):
    """生成处理摘要报告"""
    report_file = os.path.join(output_path, "basin_id_masking_summary.txt")
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("流域ID掩膜处理摘要\n")
            f.write("=" * 50 + "\n\n")
            
            f.write("处理统计:\n")
            f.write(f"- 原始有效流域ID像元数: {original_valid_count}\n")
            f.write(f"- 原始流域ID数量: {original_basin_count}\n")
            f.write(f"- 被掩膜的像元数: {masked_count}\n")
            f.write(f"- 保留的有效流域ID像元数: {new_valid_count}\n")
            f.write(f"- 保留的流域ID数量: {new_basin_count}\n")
            f.write(f"- 像元保留比例: {new_valid_count/original_valid_count*100:.2f}%\n")
            f.write(f"- 流域ID保留比例: {new_basin_count/original_basin_count*100:.2f}%\n\n")
            
            f.write("处理方法:\n")
            f.write("- 根据有效像元文件创建掩膜\n")
            f.write("- 将不在有效像元范围内的流域ID设为NoData\n")
            f.write("- 原始流域ID文件中的0值被视为无效像元\n")
            f.write("- 保持有效区域内的流域ID值不变\n\n")
            
            f.write("输出文件:\n")
            f.write("- updated_valid_pixels_basin_id.tif: 掩膜后的流域ID文件\n")
            f.write("- basin_id_masking_summary.txt: 本处理摘要\n")
        
        print(f"✓ 处理摘要已保存: {report_file}")
        
    except Exception as e:
        print(f"✗ 摘要保存失败: {e}")

def main():
    """主函数"""
    print("开始基于有效像元掩膜更新流域ID文件...")
    
    # 文件路径设置
    basin_id_file = r"Z:\yuan\clip_biaozhun_tif\clip_tif_basin4_600_1440.tif"
    valid_pixels_file = r"Z:\yuan\paper3_new02\shp\valid_pixels\updated_valid_pixels.tif"
    output_dir = r"Z:\yuan\paper3_new02\shp\valid_pixels2"
    
    # 检查并创建输出目录
    check_and_create_output_dir(output_dir)
    
    # 读取流域ID文件
    print("\n1. 读取流域ID文件...")
    basin_data, basin_profile, _, _, _, basin_nodata = read_raster_with_info(
        basin_id_file, "流域ID文件"
    )
    
    if basin_data is None:
        print("流域ID文件读取失败，程序退出")
        return
    
    # 读取有效像元文件
    print("\n2. 读取有效像元文件...")
    valid_data, valid_profile, _, _, _, valid_nodata = read_raster_with_info(
        valid_pixels_file, "有效像元文件"
    )
    
    if valid_data is None:
        print("有效像元文件读取失败，程序退出")
        return
    
    # 检查栅格兼容性
    if not check_raster_compatibility(basin_data, basin_profile, valid_data, valid_profile):
        print("栅格不兼容，程序退出")
        return
    
    # 统计原始数据（排除NoData值和0值）
    if basin_nodata is not None:
        original_basin_mask = (basin_data != basin_nodata) & (~np.isnan(basin_data)) & (basin_data != 0)
    else:
        original_basin_mask = (~np.isnan(basin_data)) & (basin_data != 0)
    
    original_valid_count = np.sum(original_basin_mask)
    original_basin_ids = np.unique(basin_data[original_basin_mask]) if original_valid_count > 0 else []
    original_basin_count = len(original_basin_ids)
    
    # 创建掩膜后的流域ID
    print("\n3. 应用有效像元掩膜...")
    new_basin_data = create_masked_basin_id(basin_data, basin_nodata, valid_data, valid_nodata)

    # 确定使用的NoData值
    if basin_nodata is not None:
        used_nodata = basin_nodata
    else:
        if new_basin_data.dtype == np.uint32:
            used_nodata = np.iinfo(np.uint32).max
        elif new_basin_data.dtype == np.int32:
            used_nodata = np.iinfo(np.int32).min
        else:
            used_nodata = np.nan

    # 统计新数据（排除NoData值和0值）
    if not np.isnan(used_nodata):
        new_basin_mask = (new_basin_data != used_nodata) & (~np.isnan(new_basin_data)) & (new_basin_data != 0)
    else:
        new_basin_mask = (~np.isnan(new_basin_data)) & (new_basin_data != 0)

    new_valid_count = np.sum(new_basin_mask)
    new_basin_ids = np.unique(new_basin_data[new_basin_mask]) if new_valid_count > 0 else []
    new_basin_count = len(new_basin_ids)
    masked_count = original_valid_count - new_valid_count

    # 保存掩膜后的流域ID文件
    print("\n4. 保存掩膜后的流域ID文件...")
    output_file = save_masked_basin_id(new_basin_data, basin_profile, output_dir,
                                     "updated_valid_pixels_basin_id.tif", used_nodata)
    
    if output_file:
        # 生成处理摘要
        print("\n5. 生成处理摘要...")
        generate_processing_summary(output_dir, original_valid_count, original_basin_count,
                                  new_valid_count, new_basin_count, masked_count)
        
        print(f"\n✓ 处理完成！")
        print(f"✓ 原始有效流域ID像元: {original_valid_count}")
        print(f"✓ 原始流域ID数量: {original_basin_count}")
        print(f"✓ 保留有效流域ID像元: {new_valid_count}")
        print(f"✓ 保留流域ID数量: {new_basin_count}")
        print(f"✓ 像元保留比例: {new_valid_count/original_valid_count*100:.2f}%")
        print(f"✓ 输出文件: {output_file}")
    else:
        print("✗ 处理失败")

if __name__ == "__main__":
    main()
