#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于验证栅格更新有效像元栅格工具
读取旧有效像元栅格和验证栅格，根据验证条件生成新的有效像元栅格
"""

import os
import numpy as np
import rasterio
from rasterio.transform import from_bounds
import warnings
warnings.filterwarnings('ignore')

def check_and_create_output_dir(output_path):
    """检查并创建输出目录"""
    if not os.path.exists(output_path):
        os.makedirs(output_path, exist_ok=True)
        print(f"创建输出目录: {output_path}")
    else:
        print(f"输出目录已存在: {output_path}")

def read_raster_with_info(file_path, data_name):
    """读取栅格数据并显示详细信息"""
    try:
        with rasterio.open(file_path) as src:
            data = src.read(1)
            profile = src.profile
            transform = src.transform
            crs = src.crs
            bounds = src.bounds
            nodata = src.nodata
            
        print(f"\n成功读取{data_name}: {os.path.basename(file_path)}")
        print(f"  - 数据形状: {data.shape}")
        print(f"  - 数据类型: {data.dtype}")
        print(f"  - CRS: {crs}")
        print(f"  - NoData值: {nodata}")
        print(f"  - 边界范围: {bounds}")
        print(f"  - 像元大小: {abs(transform[0]):.6f} × {abs(transform[4]):.6f}")
        
        # 统计有效值
        if nodata is not None:
            valid_mask = (data != nodata) & (~np.isnan(data))
        else:
            valid_mask = ~np.isnan(data)
            
        valid_count = np.sum(valid_mask)
        total_count = data.size
        
        print(f"  - 总像元数: {total_count}")
        print(f"  - 有效像元数: {valid_count}")
        print(f"  - 有效比例: {valid_count/total_count*100:.2f}%")
        
        if valid_count > 0:
            valid_data = data[valid_mask]
            print(f"  - 有效值范围: {np.min(valid_data):.4f} - {np.max(valid_data):.4f}")
            print(f"  - 有效值平均: {np.mean(valid_data):.4f}")
        
        return data, profile, transform, crs, bounds, nodata
        
    except Exception as e:
        print(f"读取{data_name}失败 {file_path}: {e}")
        return None, None, None, None, None, None

def check_raster_compatibility(old_data, old_profile, validation_data, validation_profile):
    """检查两个栅格的兼容性"""
    print("\n检查栅格兼容性...")
    
    # 检查数据形状
    if old_data.shape != validation_data.shape:
        print(f"✗ 数据形状不匹配:")
        print(f"  旧有效像元栅格: {old_data.shape}")
        print(f"  验证栅格: {validation_data.shape}")
        return False
    
    # 检查坐标系
    if old_profile['crs'] != validation_profile['crs']:
        print(f"✗ 坐标系不匹配:")
        print(f"  旧有效像元栅格: {old_profile['crs']}")
        print(f"  验证栅格: {validation_profile['crs']}")
        return False
    
    # 检查变换矩阵（允许小的数值误差）
    old_transform = old_profile['transform']
    validation_transform = validation_profile['transform']
    
    transform_diff = np.array(old_transform) - np.array(validation_transform)
    max_diff = np.max(np.abs(transform_diff))
    
    if max_diff > 1e-10:
        print(f"✗ 变换矩阵不匹配 (最大差异: {max_diff}):")
        print(f"  旧有效像元栅格: {old_transform}")
        print(f"  验证栅格: {validation_transform}")
        return False
    
    print("✓ 栅格兼容性检查通过")
    return True

def create_updated_valid_pixels(old_data, old_nodata, validation_data, validation_nodata):
    """根据验证栅格创建更新的有效像元栅格"""
    print("\n创建更新的有效像元栅格...")

    # 复制旧有效像元栅格
    new_data = old_data.copy()

    # 找到旧有效像元栅格中的有效像元位置
    if old_nodata is not None:
        old_valid_mask = (old_data != old_nodata) & (~np.isnan(old_data))
    else:
        old_valid_mask = ~np.isnan(old_data)

    print(f"旧有效像元数量: {np.sum(old_valid_mask)}")

    # 统计验证条件
    validation_check_count = 0
    invalidated_count = 0
    invalidated_by_negative = 0
    invalidated_by_nodata = 0

    # 遍历旧有效像元位置
    rows, cols = np.where(old_valid_mask)

    for row, col in zip(rows, cols):
        validation_check_count += 1

        # 获取验证栅格在该位置的值
        validation_value = validation_data[row, col]

        # 检查验证条件：如果验证栅格值小于0或为NoData，则设为NoData
        should_invalidate = False

        # 检查是否为NoData
        if np.isnan(validation_value):
            should_invalidate = True
            invalidated_by_nodata += 1
        elif validation_nodata is not None and validation_value == validation_nodata:
            should_invalidate = True
            invalidated_by_nodata += 1
        # 检查是否小于0
        elif validation_value < 0:
            should_invalidate = True
            invalidated_by_negative += 1

        # 如果需要无效化，则设为NoData
        if should_invalidate:
            if old_nodata is not None:
                new_data[row, col] = old_nodata
            else:
                new_data[row, col] = np.nan
            invalidated_count += 1

    # 统计新有效像元
    if old_nodata is not None:
        new_valid_mask = (new_data != old_nodata) & (~np.isnan(new_data))
    else:
        new_valid_mask = ~np.isnan(new_data)

    new_valid_count = np.sum(new_valid_mask)

    print(f"验证检查的像元数: {validation_check_count}")
    print(f"被无效化的像元数: {invalidated_count}")
    print(f"  - 因验证值<0无效化: {invalidated_by_negative}")
    print(f"  - 因验证值为NoData无效化: {invalidated_by_nodata}")
    print(f"新有效像元数量: {new_valid_count}")
    print(f"保留的有效像元比例: {new_valid_count/np.sum(old_valid_mask)*100:.2f}%")

    return new_data

def save_updated_raster(data, profile, output_path, filename):
    """保存更新后的栅格"""
    print(f"\n保存更新后的栅格...")
    
    output_file = os.path.join(output_path, filename)
    
    try:
        # 更新profile以确保数据类型正确
        updated_profile = profile.copy()
        updated_profile['dtype'] = data.dtype
        
        with rasterio.open(output_file, 'w', **updated_profile) as dst:
            dst.write(data, 1)
        
        print(f"✓ 栅格已保存: {output_file}")
        
        # 验证保存的文件
        with rasterio.open(output_file) as src:
            saved_data = src.read(1)
            saved_nodata = src.nodata
            
        if saved_nodata is not None:
            saved_valid_mask = (saved_data != saved_nodata) & (~np.isnan(saved_data))
        else:
            saved_valid_mask = ~np.isnan(saved_data)
            
        saved_valid_count = np.sum(saved_valid_mask)
        print(f"✓ 验证保存结果: {saved_valid_count} 个有效像元")
        
        return output_file
        
    except Exception as e:
        print(f"✗ 保存栅格失败: {e}")
        return None

def generate_summary_report(output_path, old_valid_count, new_valid_count, invalidated_count):
    """生成处理摘要报告"""
    report_file = os.path.join(output_path, "processing_summary.txt")
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("有效像元栅格更新处理摘要\n")
            f.write("=" * 50 + "\n\n")
            
            f.write("处理统计:\n")
            f.write(f"- 原始有效像元数: {old_valid_count}\n")
            f.write(f"- 被验证无效化的像元数: {invalidated_count}\n")
            f.write(f"- 最终有效像元数: {new_valid_count}\n")
            f.write(f"- 保留比例: {new_valid_count/old_valid_count*100:.2f}%\n")
            f.write(f"- 无效化比例: {invalidated_count/old_valid_count*100:.2f}%\n\n")
            
            f.write("验证条件:\n")
            f.write("- 验证栅格值 < 0 的像元被设为NoData\n")
            f.write("- 验证栅格值为NoData的像元被设为NoData\n")
            f.write("- 验证栅格值 ≥ 0 且非NoData的像元保持有效\n\n")
            
            f.write("输出文件:\n")
            f.write("- updated_valid_pixels.tif: 更新后的有效像元栅格\n")
            f.write("- processing_summary.txt: 本处理摘要\n")
        
        print(f"✓ 处理摘要已保存: {report_file}")
        
    except Exception as e:
        print(f"✗ 摘要保存失败: {e}")

def main():
    """主函数"""
    print("开始处理有效像元栅格更新...")
    
    # 文件路径设置
    old_valid_pixels_file = r"Z:\yuan\clip_biaozhun_tif\clip_tif_1_basin4_600_1440.tif"
    validation_file = r"Z:\yuan\ERA5-Land\fbl_025\EPEs\yz_600_1440\pre_yz_90_1971_2020.tif"
    output_dir = r"Z:\yuan\paper3_new02\shp\valid_pixels"
    
    # 检查并创建输出目录
    check_and_create_output_dir(output_dir)
    
    # 读取旧有效像元栅格
    print("\n1. 读取旧有效像元栅格...")
    old_data, old_profile, _, _, _, old_nodata = read_raster_with_info(
        old_valid_pixels_file, "旧有效像元栅格"
    )
    
    if old_data is None:
        print("旧有效像元栅格读取失败，程序退出")
        return
    
    # 读取验证栅格
    print("\n2. 读取验证栅格...")
    validation_data, validation_profile, _, _, _, validation_nodata = read_raster_with_info(
        validation_file, "验证栅格"
    )
    
    if validation_data is None:
        print("验证栅格读取失败，程序退出")
        return
    
    # 检查栅格兼容性
    if not check_raster_compatibility(old_data, old_profile, validation_data, validation_profile):
        print("栅格不兼容，程序退出")
        return
    
    # 统计原始有效像元数
    if old_nodata is not None:
        old_valid_mask = (old_data != old_nodata) & (~np.isnan(old_data))
    else:
        old_valid_mask = ~np.isnan(old_data)
    old_valid_count = np.sum(old_valid_mask)
    
    # 创建更新的有效像元栅格
    print("\n3. 根据验证条件更新有效像元栅格...")
    new_data = create_updated_valid_pixels(old_data, old_nodata, validation_data, validation_nodata)
    
    # 统计新有效像元数
    if old_nodata is not None:
        new_valid_mask = (new_data != old_nodata) & (~np.isnan(new_data))
    else:
        new_valid_mask = ~np.isnan(new_data)
    new_valid_count = np.sum(new_valid_mask)
    invalidated_count = old_valid_count - new_valid_count
    
    # 保存更新后的栅格
    print("\n4. 保存更新后的有效像元栅格...")
    output_file = save_updated_raster(new_data, old_profile, output_dir, "updated_valid_pixels.tif")
    
    if output_file:
        # 生成处理摘要
        print("\n5. 生成处理摘要...")
        generate_summary_report(output_dir, old_valid_count, new_valid_count, invalidated_count)
        
        print(f"\n✓ 处理完成！")
        print(f"✓ 原始有效像元: {old_valid_count}")
        print(f"✓ 最终有效像元: {new_valid_count}")
        print(f"✓ 无效化像元: {invalidated_count}")
        print(f"✓ 保留比例: {new_valid_count/old_valid_count*100:.2f}%")
        print(f"✓ 输出文件: {output_file}")
    else:
        print("✗ 处理失败")

if __name__ == "__main__":
    main()
