#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分析流域ID丢失原因工具
详细分析为什么掩膜少量像元会导致大量流域ID丢失
"""

import os
import numpy as np
import rasterio
import pandas as pd
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def read_raster_data(file_path, name):
    """读取栅格数据"""
    with rasterio.open(file_path) as src:
        data = src.read(1)
        nodata = src.nodata
    print(f"读取{name}: {os.path.basename(file_path)}")
    return data, nodata

def analyze_basin_id_distribution(basin_data, basin_nodata, valid_data, valid_nodata):
    """分析流域ID分布和丢失原因"""
    print("\n=== 流域ID分布分析 ===")
    
    # 创建掩膜
    if valid_nodata is not None:
        valid_mask = (valid_data != valid_nodata) & (~np.isnan(valid_data))
    else:
        valid_mask = ~np.isnan(valid_data)
    
    # 原始流域ID掩膜（排除0值）
    if basin_nodata is not None:
        original_basin_mask = (basin_data != basin_nodata) & (~np.isnan(basin_data)) & (basin_data != 0)
    else:
        original_basin_mask = (~np.isnan(basin_data)) & (basin_data != 0)
    
    # 被掩膜的位置
    masked_positions = original_basin_mask & (~valid_mask)
    
    print(f"原始有效流域ID像元数: {np.sum(original_basin_mask)}")
    print(f"有效像元掩膜数: {np.sum(valid_mask)}")
    print(f"被掩膜的流域ID像元数: {np.sum(masked_positions)}")
    
    # 分析原始流域ID
    original_basin_ids = basin_data[original_basin_mask]
    unique_original_ids, original_counts = np.unique(original_basin_ids, return_counts=True)
    
    print(f"\n原始流域ID统计:")
    print(f"  唯一流域ID数量: {len(unique_original_ids)}")
    print(f"  流域ID像元数范围: {np.min(original_counts)} - {np.max(original_counts)}")
    print(f"  平均每个流域ID的像元数: {np.mean(original_counts):.2f}")
    
    # 分析被掩膜位置的流域ID
    if np.sum(masked_positions) > 0:
        masked_basin_ids = basin_data[masked_positions]
        unique_masked_ids, masked_counts = np.unique(masked_basin_ids, return_counts=True)
        
        print(f"\n被掩膜位置的流域ID统计:")
        print(f"  被掩膜位置包含的唯一流域ID数量: {len(unique_masked_ids)}")
        print(f"  被掩膜的流域ID: {unique_masked_ids}")
        print(f"  每个被掩膜流域ID的像元数: {masked_counts}")
        
        # 分析哪些流域ID完全丢失
        remaining_basin_ids = basin_data[original_basin_mask & valid_mask]
        unique_remaining_ids = np.unique(remaining_basin_ids)
        
        completely_lost_ids = set(unique_masked_ids) - set(unique_remaining_ids)
        partially_lost_ids = set(unique_masked_ids) & set(unique_remaining_ids)
        
        print(f"\n流域ID丢失分析:")
        print(f"  完全丢失的流域ID数量: {len(completely_lost_ids)}")
        print(f"  部分丢失的流域ID数量: {len(partially_lost_ids)}")
        
        if completely_lost_ids:
            print(f"  完全丢失的流域ID: {sorted(completely_lost_ids)}")
            
            # 分析完全丢失的流域ID在原始数据中的像元数
            lost_id_counts = []
            for lost_id in completely_lost_ids:
                count = np.sum(original_basin_ids == lost_id)
                lost_id_counts.append(count)
            
            print(f"  完全丢失流域ID的原始像元数: {lost_id_counts}")
            print(f"  完全丢失流域ID的像元数范围: {min(lost_id_counts)} - {max(lost_id_counts)}")
            print(f"  完全丢失流域ID的平均像元数: {np.mean(lost_id_counts):.2f}")
        
        if partially_lost_ids:
            print(f"  部分丢失的流域ID: {sorted(partially_lost_ids)}")
            
            # 分析部分丢失的详情
            for partial_id in sorted(partially_lost_ids):
                original_count = np.sum(original_basin_ids == partial_id)
                remaining_count = np.sum(remaining_basin_ids == partial_id)
                lost_count = original_count - remaining_count
                print(f"    流域ID {partial_id}: 原始{original_count}像元, 保留{remaining_count}像元, 丢失{lost_count}像元")
    
    # 分析流域ID大小分布
    print(f"\n流域ID大小分布分析:")
    size_ranges = [(1, 1), (2, 5), (6, 10), (11, 50), (51, 100), (101, float('inf'))]
    
    for min_size, max_size in size_ranges:
        if max_size == float('inf'):
            mask = original_counts >= min_size
            range_str = f"{min_size}+"
        else:
            mask = (original_counts >= min_size) & (original_counts <= max_size)
            range_str = f"{min_size}-{max_size}"
        
        count = np.sum(mask)
        total_pixels = np.sum(original_counts[mask])
        print(f"  {range_str}像元的流域数量: {count}, 总像元数: {total_pixels}")
    
    # 创建可视化
    create_analysis_visualization(basin_data, original_basin_mask, valid_mask, masked_positions, 
                                unique_original_ids, original_counts)
    
    return unique_original_ids, unique_remaining_ids, completely_lost_ids

def create_analysis_visualization(basin_data, original_mask, valid_mask, masked_positions, 
                                unique_ids, counts):
    """创建分析可视化图"""
    print(f"\n创建分析可视化图...")
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 子图1: 原始流域ID分布
    ax1 = axes[0, 0]
    display_data = np.where(original_mask, basin_data, np.nan)
    im1 = ax1.imshow(display_data, cmap='tab20', interpolation='nearest')
    ax1.set_title(f'原始流域ID分布\n有效像元: {np.sum(original_mask)}', fontweight='bold')
    ax1.set_xlabel('列')
    ax1.set_ylabel('行')
    
    # 子图2: 有效像元掩膜
    ax2 = axes[0, 1]
    mask_display = np.zeros_like(basin_data, dtype=int)
    mask_display[valid_mask] = 1
    mask_display[masked_positions] = 2
    
    colors = ['white', 'green', 'red']
    from matplotlib.colors import ListedColormap
    cmap = ListedColormap(colors)
    
    im2 = ax2.imshow(mask_display, cmap=cmap, vmin=0, vmax=2, interpolation='nearest')
    ax2.set_title(f'掩膜分析\n绿色:保留, 红色:被掩膜', fontweight='bold')
    ax2.set_xlabel('列')
    ax2.set_ylabel('行')
    
    # 子图3: 流域ID大小分布直方图
    ax3 = axes[1, 0]
    ax3.hist(counts, bins=50, alpha=0.7, edgecolor='black')
    ax3.set_xlabel('流域ID像元数')
    ax3.set_ylabel('流域数量')
    ax3.set_title('流域ID大小分布', fontweight='bold')
    ax3.set_yscale('log')
    
    # 添加统计信息
    ax3.axvline(np.mean(counts), color='red', linestyle='--', label=f'平均值: {np.mean(counts):.1f}')
    ax3.axvline(np.median(counts), color='orange', linestyle='--', label=f'中位数: {np.median(counts):.1f}')
    ax3.legend()
    
    # 子图4: 小流域统计
    ax4 = axes[1, 1]
    size_ranges = [1, 2, 3, 4, 5, 10, 20, 50, 100]
    range_counts = []
    
    for i, size in enumerate(size_ranges):
        if i == 0:
            count = np.sum(counts == size)
        else:
            prev_size = size_ranges[i-1]
            count = np.sum((counts > prev_size) & (counts <= size))
        range_counts.append(count)
    
    # 添加最后一个范围
    range_counts.append(np.sum(counts > size_ranges[-1]))
    size_labels = [str(s) for s in size_ranges] + [f'>{size_ranges[-1]}']
    
    bars = ax4.bar(range(len(range_counts)), range_counts)
    ax4.set_xlabel('流域大小范围')
    ax4.set_ylabel('流域数量')
    ax4.set_title('不同大小流域的数量分布', fontweight='bold')
    ax4.set_xticks(range(len(size_labels)))
    ax4.set_xticklabels(size_labels, rotation=45)
    
    # 在柱状图上添加数值
    for bar, count in zip(bars, range_counts):
        if count > 0:
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + max(range_counts)*0.01,
                    f'{count}', ha='center', va='bottom', fontsize=8)
    
    plt.tight_layout()
    
    # 保存图片
    output_file = "basin_id_loss_analysis.png"
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"✓ 分析图已保存: {output_file}")
    
    plt.show()

def generate_detailed_report(original_ids, remaining_ids, lost_ids, output_dir):
    """生成详细分析报告"""
    report_file = os.path.join(output_dir, "basin_id_loss_analysis_report.txt")
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("流域ID丢失详细分析报告\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"总体统计:\n")
            f.write(f"- 原始流域ID数量: {len(original_ids)}\n")
            f.write(f"- 保留流域ID数量: {len(remaining_ids)}\n")
            f.write(f"- 完全丢失流域ID数量: {len(lost_ids)}\n")
            f.write(f"- 流域ID保留率: {len(remaining_ids)/len(original_ids)*100:.2f}%\n\n")
            
            if lost_ids:
                f.write(f"完全丢失的流域ID列表:\n")
                for lost_id in sorted(lost_ids):
                    f.write(f"- {lost_id}\n")
                f.write(f"\n")
            
            f.write(f"结论:\n")
            f.write(f"- 虽然只掩膜了少量像元，但这些像元包含了许多独特的流域ID\n")
            f.write(f"- 被掩膜的流域ID可能是小流域或边界流域\n")
            f.write(f"- 建议检查被掩膜位置是否位于流域边界或特殊区域\n")
        
        print(f"✓ 详细报告已保存: {report_file}")
        
    except Exception as e:
        print(f"✗ 报告保存失败: {e}")

def main():
    """主函数"""
    print("开始分析流域ID丢失原因...")
    
    # 文件路径
    basin_id_file = r"Z:\yuan\clip_biaozhun_tif\clip_tif_basin4_600_1440.tif"
    valid_pixels_file = r"Z:\yuan\paper3_new02\shp\valid_pixels\updated_valid_pixels.tif"
    output_dir = r"Z:\yuan\paper3_new02\shp\valid_pixels2"
    
    # 读取数据
    basin_data, basin_nodata = read_raster_data(basin_id_file, "流域ID文件")
    valid_data, valid_nodata = read_raster_data(valid_pixels_file, "有效像元文件")
    
    # 分析流域ID分布和丢失原因
    original_ids, remaining_ids, lost_ids = analyze_basin_id_distribution(
        basin_data, basin_nodata, valid_data, valid_nodata
    )
    
    # 生成详细报告
    generate_detailed_report(original_ids, remaining_ids, lost_ids, output_dir)
    
    # 计算被掩膜的像元数
    if basin_nodata is not None:
        original_basin_mask = (basin_data != basin_nodata) & (~np.isnan(basin_data)) & (basin_data != 0)
    else:
        original_basin_mask = (~np.isnan(basin_data)) & (basin_data != 0)

    if valid_nodata is not None:
        valid_mask = (valid_data != valid_nodata) & (~np.isnan(valid_data))
    else:
        valid_mask = ~np.isnan(valid_data)

    masked_pixels = np.sum(original_basin_mask & (~valid_mask))

    print(f"\n✓ 分析完成！")
    print(f"✓ 主要发现: 被掩膜的{masked_pixels}个像元包含了{len(lost_ids)}个独特的流域ID")
    print(f"✓ 这说明被掩膜的像元主要是小流域或流域边界像元")

if __name__ == "__main__":
    main()
