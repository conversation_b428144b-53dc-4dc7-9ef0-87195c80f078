#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
流域ID文件对比分析工具
读取两个流域ID文件，分析它们的相同和不同之处
"""

import os
import numpy as np
import rasterio
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

def read_basin_id_raster(file_path, file_name):
    """读取流域ID栅格数据"""
    try:
        with rasterio.open(file_path) as src:
            data = src.read(1)
            profile = src.profile
            transform = src.transform
            crs = src.crs
            bounds = src.bounds
            nodata = src.nodata
            
        print(f"\n成功读取{file_name}: {os.path.basename(file_path)}")
        print(f"  - 数据形状: {data.shape}")
        print(f"  - 数据类型: {data.dtype}")
        print(f"  - CRS: {crs}")
        print(f"  - NoData值: {nodata}")
        print(f"  - 边界范围: {bounds}")
        
        # 统计有效值和唯一值
        if nodata is not None:
            valid_mask = (data != nodata) & (~np.isnan(data))
        else:
            valid_mask = ~np.isnan(data)
            
        valid_count = np.sum(valid_mask)
        total_count = data.size
        
        print(f"  - 总像元数: {total_count}")
        print(f"  - 有效像元数: {valid_count}")
        print(f"  - 有效比例: {valid_count/total_count*100:.2f}%")
        
        if valid_count > 0:
            valid_data = data[valid_mask]
            unique_values = np.unique(valid_data)
            print(f"  - 唯一值数量: {len(unique_values)}")
            print(f"  - 唯一值范围: {np.min(unique_values)} - {np.max(unique_values)}")
            if len(unique_values) <= 20:
                print(f"  - 唯一值列表: {unique_values}")
            else:
                print(f"  - 前10个唯一值: {unique_values[:10]}")
                print(f"  - 后10个唯一值: {unique_values[-10:]}")
        
        return data, profile, transform, crs, bounds, nodata, valid_mask
        
    except Exception as e:
        print(f"读取{file_name}失败 {file_path}: {e}")
        return None, None, None, None, None, None, None

def check_raster_compatibility(data1, profile1, data2, profile2, name1, name2):
    """检查两个栅格的兼容性"""
    print(f"\n检查 {name1} 和 {name2} 的兼容性...")
    
    compatible = True
    
    # 检查数据形状
    if data1.shape != data2.shape:
        print(f"✗ 数据形状不匹配:")
        print(f"  {name1}: {data1.shape}")
        print(f"  {name2}: {data2.shape}")
        compatible = False
    else:
        print(f"✓ 数据形状匹配: {data1.shape}")
    
    # 检查坐标系
    if profile1['crs'] != profile2['crs']:
        print(f"✗ 坐标系不匹配:")
        print(f"  {name1}: {profile1['crs']}")
        print(f"  {name2}: {profile2['crs']}")
        compatible = False
    else:
        print(f"✓ 坐标系匹配: {profile1['crs']}")
    
    # 检查变换矩阵
    transform1 = profile1['transform']
    transform2 = profile2['transform']
    
    transform_diff = np.array(transform1) - np.array(transform2)
    max_diff = np.max(np.abs(transform_diff))
    
    if max_diff > 1e-10:
        print(f"✗ 变换矩阵不匹配 (最大差异: {max_diff})")
        compatible = False
    else:
        print(f"✓ 变换矩阵匹配")
    
    return compatible

def analyze_basin_differences(data1, mask1, nodata1, data2, mask2, nodata2, name1, name2):
    """分析两个流域ID文件的差异"""
    print(f"\n分析 {name1} 和 {name2} 的差异...")
    
    # 创建统一的有效掩膜
    combined_mask = mask1 | mask2
    
    # 获取有效数据
    if nodata1 is not None:
        data1_clean = np.where(mask1, data1, np.nan)
    else:
        data1_clean = np.where(mask1, data1, np.nan)
        
    if nodata2 is not None:
        data2_clean = np.where(mask2, data2, np.nan)
    else:
        data2_clean = np.where(mask2, data2, np.nan)
    
    # 统计分析
    print(f"\n基本统计:")
    print(f"  {name1} 有效像元: {np.sum(mask1)}")
    print(f"  {name2} 有效像元: {np.sum(mask2)}")
    print(f"  两者都有效的像元: {np.sum(mask1 & mask2)}")
    print(f"  只有{name1}有效的像元: {np.sum(mask1 & ~mask2)}")
    print(f"  只有{name2}有效的像元: {np.sum(~mask1 & mask2)}")
    print(f"  两者都无效的像元: {np.sum(~mask1 & ~mask2)}")
    
    # 在两者都有效的区域比较值
    both_valid = mask1 & mask2
    if np.sum(both_valid) > 0:
        values1_both = data1_clean[both_valid]
        values2_both = data2_clean[both_valid]
        
        # 检查值是否相同
        same_values = values1_both == values2_both
        same_count = np.sum(same_values)
        different_count = np.sum(~same_values)
        
        print(f"\n在两者都有效的区域中:")
        print(f"  相同值的像元数: {same_count}")
        print(f"  不同值的像元数: {different_count}")
        print(f"  相同比例: {same_count/len(values1_both)*100:.2f}%")
        
        # 分析不同值的情况
        if different_count > 0:
            diff_values1 = values1_both[~same_values]
            diff_values2 = values2_both[~same_values]
            
            print(f"\n不同值分析:")
            print(f"  {name1}中的不同值范围: {np.min(diff_values1)} - {np.max(diff_values1)}")
            print(f"  {name2}中的不同值范围: {np.min(diff_values2)} - {np.max(diff_values2)}")
            
            # 显示一些不同值的例子
            if len(diff_values1) <= 20:
                print(f"  所有不同值对:")
                for i in range(len(diff_values1)):
                    print(f"    {name1}: {diff_values1[i]}, {name2}: {diff_values2[i]}")
            else:
                print(f"  前10个不同值对:")
                for i in range(10):
                    print(f"    {name1}: {diff_values1[i]}, {name2}: {diff_values2[i]}")
    
    # 分析唯一值
    print(f"\n唯一值分析:")
    if np.sum(mask1) > 0:
        unique1 = np.unique(data1_clean[mask1])
        print(f"  {name1} 唯一值数量: {len(unique1)}")
        if len(unique1) <= 20:
            print(f"  {name1} 唯一值: {unique1}")
    
    if np.sum(mask2) > 0:
        unique2 = np.unique(data2_clean[mask2])
        print(f"  {name2} 唯一值数量: {len(unique2)}")
        if len(unique2) <= 20:
            print(f"  {name2} 唯一值: {unique2}")
    
    # 如果两者都有有效值，分析交集和差集
    if np.sum(mask1) > 0 and np.sum(mask2) > 0:
        unique1 = set(data1_clean[mask1])
        unique2 = set(data2_clean[mask2])
        
        common_values = unique1 & unique2
        only_in_1 = unique1 - unique2
        only_in_2 = unique2 - unique1
        
        print(f"\n唯一值集合分析:")
        print(f"  共同的唯一值数量: {len(common_values)}")
        print(f"  只在{name1}中的唯一值数量: {len(only_in_1)}")
        print(f"  只在{name2}中的唯一值数量: {len(only_in_2)}")
        
        if len(common_values) <= 20:
            print(f"  共同的唯一值: {sorted(common_values)}")
        if len(only_in_1) <= 20 and len(only_in_1) > 0:
            print(f"  只在{name1}中的唯一值: {sorted(only_in_1)}")
        if len(only_in_2) <= 20 and len(only_in_2) > 0:
            print(f"  只在{name2}中的唯一值: {sorted(only_in_2)}")
    
    return both_valid, same_values if 'same_values' in locals() else None

def create_comparison_visualization(data1, mask1, data2, mask2, both_valid, same_values, name1, name2):
    """创建对比可视化图"""
    print(f"\n创建对比可视化图...")
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 子图1: 第一个文件
    ax1 = axes[0, 0]
    im1 = ax1.imshow(np.where(mask1, data1, np.nan), cmap='tab20', interpolation='nearest')
    ax1.set_title(f'{name1}\n有效像元: {np.sum(mask1)}', fontsize=12, fontweight='bold')
    ax1.set_xlabel('列')
    ax1.set_ylabel('行')
    plt.colorbar(im1, ax=ax1, shrink=0.8)
    
    # 子图2: 第二个文件
    ax2 = axes[0, 1]
    im2 = ax2.imshow(np.where(mask2, data2, np.nan), cmap='tab20', interpolation='nearest')
    ax2.set_title(f'{name2}\n有效像元: {np.sum(mask2)}', fontsize=12, fontweight='bold')
    ax2.set_xlabel('列')
    ax2.set_ylabel('行')
    plt.colorbar(im2, ax=ax2, shrink=0.8)
    
    # 子图3: 差异分析
    ax3 = axes[1, 0]
    # 创建差异图：0=两者都无效，1=只有文件1有效，2=只有文件2有效，3=两者都有效且相同，4=两者都有效但不同
    diff_map = np.zeros_like(data1, dtype=int)
    diff_map[mask1 & ~mask2] = 1  # 只有文件1有效
    diff_map[~mask1 & mask2] = 2  # 只有文件2有效
    if both_valid is not None and same_values is not None:
        diff_map[both_valid & same_values] = 3  # 两者都有效且相同
        diff_map[both_valid & ~same_values] = 4  # 两者都有效但不同
    else:
        diff_map[both_valid] = 3  # 两者都有效
    
    colors = ['white', 'red', 'blue', 'green', 'orange']
    labels = ['两者都无效', f'只有{name1}有效', f'只有{name2}有效', '两者相同', '两者不同']
    cmap = ListedColormap(colors)
    
    im3 = ax3.imshow(diff_map, cmap=cmap, vmin=0, vmax=4, interpolation='nearest')
    ax3.set_title('差异分析', fontsize=12, fontweight='bold')
    ax3.set_xlabel('列')
    ax3.set_ylabel('行')
    
    # 添加图例
    from matplotlib.patches import Patch
    legend_elements = [Patch(facecolor=colors[i], label=labels[i]) for i in range(len(colors))]
    ax3.legend(handles=legend_elements, loc='center left', bbox_to_anchor=(1, 0.5))
    
    # 子图4: 统计柱状图
    ax4 = axes[1, 1]
    categories = ['两者都无效', f'只有{name1}', f'只有{name2}', '两者相同', '两者不同']
    counts = [
        np.sum(~mask1 & ~mask2),
        np.sum(mask1 & ~mask2),
        np.sum(~mask1 & mask2),
        np.sum(diff_map == 3),
        np.sum(diff_map == 4)
    ]
    
    bars = ax4.bar(range(len(categories)), counts, color=colors)
    ax4.set_title('像元数量统计', fontsize=12, fontweight='bold')
    ax4.set_ylabel('像元数量')
    ax4.set_xticks(range(len(categories)))
    ax4.set_xticklabels(categories, rotation=45, ha='right')
    
    # 在柱状图上添加数值标签
    for bar, count in zip(bars, counts):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + max(counts)*0.01,
                f'{count}', ha='center', va='bottom')
    
    plt.tight_layout()
    
    # 保存图片
    output_file = "basin_id_comparison.png"
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"✓ 对比图已保存: {output_file}")
    
    plt.show()

def generate_comparison_report(name1, name2, mask1, mask2, both_valid, same_values):
    """生成对比分析报告"""
    report_file = "basin_id_comparison_report.txt"
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("流域ID文件对比分析报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"对比文件:\n")
            f.write(f"- 文件1: {name1}\n")
            f.write(f"- 文件2: {name2}\n\n")
            
            f.write(f"基本统计:\n")
            f.write(f"- {name1} 有效像元数: {np.sum(mask1)}\n")
            f.write(f"- {name2} 有效像元数: {np.sum(mask2)}\n")
            f.write(f"- 两者都有效的像元数: {np.sum(both_valid) if both_valid is not None else 0}\n")
            f.write(f"- 只有{name1}有效的像元数: {np.sum(mask1 & ~mask2)}\n")
            f.write(f"- 只有{name2}有效的像元数: {np.sum(~mask1 & mask2)}\n")
            f.write(f"- 两者都无效的像元数: {np.sum(~mask1 & ~mask2)}\n\n")
            
            if both_valid is not None and same_values is not None:
                f.write(f"值比较分析:\n")
                f.write(f"- 在两者都有效区域中相同值的像元数: {np.sum(same_values)}\n")
                f.write(f"- 在两者都有效区域中不同值的像元数: {np.sum(~same_values)}\n")
                f.write(f"- 相同值比例: {np.sum(same_values)/len(same_values)*100:.2f}%\n\n")
            
            f.write(f"输出文件:\n")
            f.write(f"- basin_id_comparison.png: 对比可视化图\n")
            f.write(f"- basin_id_comparison_report.txt: 本分析报告\n")
        
        print(f"✓ 对比报告已保存: {report_file}")
        
    except Exception as e:
        print(f"✗ 报告保存失败: {e}")

def main():
    """主函数"""
    print("开始流域ID文件对比分析...")
    
    # 文件路径设置
    file1_path = r"Z:\yuan\clip_biaozhun_tif\clip_tif_basin4_600_1440.tif"
    file2_path = r"Z:\yuan\paper3_new02\shp\valid_pixels\updated_valid_pixels_basin_id.tif"
    
    file1_name = "原始流域ID文件"
    file2_name = "更新流域ID文件"
    
    # 读取第一个文件
    print("\n1. 读取第一个流域ID文件...")
    data1, profile1, transform1, crs1, bounds1, nodata1, mask1 = read_basin_id_raster(
        file1_path, file1_name
    )
    
    if data1 is None:
        print("第一个文件读取失败，程序退出")
        return
    
    # 读取第二个文件
    print("\n2. 读取第二个流域ID文件...")
    data2, profile2, transform2, crs2, bounds2, nodata2, mask2 = read_basin_id_raster(
        file2_path, file2_name
    )
    
    if data2 is None:
        print("第二个文件读取失败，程序退出")
        return
    
    # 检查兼容性
    if not check_raster_compatibility(data1, profile1, data2, profile2, file1_name, file2_name):
        print("文件不兼容，但继续进行可能的分析...")
    
    # 分析差异
    print("\n3. 分析流域ID差异...")
    both_valid, same_values = analyze_basin_differences(
        data1, mask1, nodata1, data2, mask2, nodata2, file1_name, file2_name
    )
    
    # 创建可视化
    print("\n4. 创建对比可视化...")
    create_comparison_visualization(
        data1, mask1, data2, mask2, both_valid, same_values, file1_name, file2_name
    )
    
    # 生成报告
    print("\n5. 生成对比报告...")
    generate_comparison_report(file1_name, file2_name, mask1, mask2, both_valid, same_values)
    
    print(f"\n✓ 流域ID文件对比分析完成！")
    print(f"✓ 输出文件: basin_id_comparison.png, basin_id_comparison_report.txt")

if __name__ == "__main__":
    main()
