#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
提取shapefile中最外部边界的脚本
读取basins_lev05.shp，提取最外部边界，保存为basins_lev05_boundary.shp
"""

import geopandas as gpd
from shapely.ops import unary_union
import os

def extract_study_area_boundary(input_shp, output_shp):
    """
    提取shapefile中所有几何体合并后的研究区面状边界

    Parameters:
    -----------
    input_shp : str
        输入shapefile路径
    output_shp : str
        输出shapefile路径
    """

    print(f"正在读取文件: {input_shp}")

    # 读取shapefile
    basins = gpd.read_file(input_shp)

    print(f"原始数据包含 {len(basins)} 个要素")
    print(f"坐标系统: {basins.crs}")

    # 合并所有几何体创建研究区
    print("正在合并所有几何体创建研究区...")
    study_area = unary_union(basins.geometry)

    # 计算研究区面积
    if hasattr(study_area, 'area'):
        area_km2 = study_area.area / 1e6
        print(f"研究区面积：{area_km2:.2f} km²")

    # 创建研究区边界的GeoDataFrame
    print("正在创建研究区边界...")
    study_area_gdf = gpd.GeoDataFrame(
        {'id': [1]},
        geometry=[study_area],
        crs=basins.crs
    )

    print("研究区总边界创建完成")

    # 确保输出目录存在
    output_dir = os.path.dirname(output_shp)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 保存研究区边界
    print(f"正在保存研究区边界到: {output_shp}")
    study_area_gdf.to_file(output_shp)

    print("处理完成！")
    print(f"研究区边界已保存：{output_shp}")
    print(f"边界几何体类型: {type(study_area).__name__}")

def main():
    """主函数"""
    
    # 输入和输出文件路径
    input_file = r"Z:\yuan\paper3_new02\shp\basins_lev01-12\basins_lev05.shp"
    output_file = r"Z:\yuan\paper3_new02\shp\basins_lev01-12\basins_lev05_boundary.shp"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件不存在 - {input_file}")
        return
    
    try:
        # 提取研究区边界
        extract_study_area_boundary(input_file, output_file)
        
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
