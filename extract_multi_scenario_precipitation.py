#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
多情景降水数据提取工具
提取历史和4个未来情景(SSP126, SSP245, SSP370, SSP585)的降水数据到渔网
"""

import os
import numpy as np
import rasterio
import geopandas as gpd
import pandas as pd
from rasterio.sample import sample_gen
import warnings
warnings.filterwarnings('ignore')

def check_and_create_output_dir(output_path):
    """检查并创建输出目录"""
    if not os.path.exists(output_path):
        os.makedirs(output_path, exist_ok=True)
        print(f"创建输出目录: {output_path}")
    else:
        print(f"输出目录已存在: {output_path}")

def read_fishnet_shapefile(shapefile_path):
    """读取渔网shapefile"""
    try:
        fishnet_gdf = gpd.read_file(shapefile_path)
        print(f"成功读取渔网文件: {os.path.basename(shapefile_path)}")
        print(f"  - 网格数量: {len(fishnet_gdf)}")
        print(f"  - CRS: {fishnet_gdf.crs}")
        print(f"  - 现有字段: {list(fishnet_gdf.columns)}")
        
        # 显示边界范围
        bounds = fishnet_gdf.total_bounds
        print(f"  - 边界范围: [{bounds[0]:.6f}, {bounds[1]:.6f}, {bounds[2]:.6f}, {bounds[3]:.6f}]")
        
        return fishnet_gdf
    except Exception as e:
        print(f"读取渔网文件失败 {shapefile_path}: {e}")
        return None

def read_raster_data(file_path):
    """读取栅格数据"""
    try:
        with rasterio.open(file_path) as src:
            data = src.read(1)
            transform = src.transform
            crs = src.crs
            bounds = src.bounds
            nodata = src.nodata
            
        print(f"成功读取栅格: {os.path.basename(file_path)}")
        print(f"  - 数据形状: {data.shape}")
        print(f"  - CRS: {crs}")
        print(f"  - 数据范围: {np.nanmin(data):.2f} - {np.nanmax(data):.2f}")
        print(f"  - NoData值: {nodata}")
        
        return data, transform, crs, bounds, nodata
    except Exception as e:
        print(f"读取栅格文件失败 {file_path}: {e}")
        return None, None, None, None, None

def extract_raster_values_to_fishnet(fishnet_gdf, raster_file, field_name):
    """从栅格提取值到渔网"""
    print(f"\n提取 {field_name} 数据...")
    
    # 读取栅格数据
    data, transform, crs, bounds, nodata = read_raster_data(raster_file)
    if data is None:
        print(f"无法读取栅格文件: {raster_file}")
        return fishnet_gdf
    
    # 检查坐标系是否一致
    if fishnet_gdf.crs != crs:
        print(f"  坐标系不一致，将渔网从 {fishnet_gdf.crs} 转换为 {crs}")
        fishnet_gdf_proj = fishnet_gdf.to_crs(crs)
    else:
        fishnet_gdf_proj = fishnet_gdf.copy()
    
    # 获取网格中心点坐标
    centroids = fishnet_gdf_proj.geometry.centroid
    coords = [(point.x, point.y) for point in centroids]
    
    print(f"  提取 {len(coords)} 个点的栅格值...")
    
    # 使用rasterio的sample函数提取值
    try:
        with rasterio.open(raster_file) as src:
            # 提取栅格值
            sampled_values = list(sample_gen(src, coords))
            values = [val[0] if val.size > 0 else np.nan for val in sampled_values]
        
        # 处理NoData值
        if nodata is not None:
            values = [np.nan if val == nodata else val for val in values]
        
        # 添加到原始GeoDataFrame（保持原始坐标系）
        fishnet_gdf[field_name] = values
        
        # 统计信息
        valid_values = [v for v in values if not np.isnan(v)]
        print(f"  有效值数量: {len(valid_values)}/{len(values)}")
        if valid_values:
            print(f"  数据范围: {min(valid_values):.2f} - {max(valid_values):.2f}")
            print(f"  平均值: {np.mean(valid_values):.2f}")
        
    except Exception as e:
        print(f"  提取栅格值时出错: {e}")
        # 如果提取失败，添加空值列
        fishnet_gdf[field_name] = np.nan
    
    return fishnet_gdf

def export_fishnet_data(fishnet_gdf, output_dir, base_name="multi_scenario_precipitation"):
    """导出渔网数据"""
    print(f"\n导出渔网数据到: {output_dir}")
    
    # 导出Shapefile
    shapefile_path = os.path.join(output_dir, f"{base_name}.shp")
    try:
        fishnet_gdf.to_file(shapefile_path, encoding='utf-8')
        print(f"✓ Shapefile已保存: {shapefile_path}")
    except Exception as e:
        print(f"✗ Shapefile保存失败: {e}")
    
    # 导出GeoJSON
    geojson_path = os.path.join(output_dir, f"{base_name}.geojson")
    try:
        fishnet_gdf.to_file(geojson_path, driver='GeoJSON', encoding='utf-8')
        print(f"✓ GeoJSON已保存: {geojson_path}")
    except Exception as e:
        print(f"✗ GeoJSON保存失败: {e}")
    
    # 导出CSV（不包含几何信息）
    csv_path = os.path.join(output_dir, f"{base_name}_data.csv")
    try:
        data_df = fishnet_gdf.drop('geometry', axis=1)
        data_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"✓ CSV数据已保存: {csv_path}")
    except Exception as e:
        print(f"✗ CSV保存失败: {e}")
    
    # 导出Excel
    excel_path = os.path.join(output_dir, f"{base_name}_data.xlsx")
    try:
        data_df = fishnet_gdf.drop('geometry', axis=1)
        data_df.to_excel(excel_path, index=False, engine='openpyxl')
        print(f"✓ Excel数据已保存: {excel_path}")
    except Exception as e:
        print(f"✗ Excel保存失败: {e}")
    
    # 生成字段说明文件
    generate_field_description(fishnet_gdf, output_dir, base_name)

def generate_field_description(fishnet_gdf, output_dir, base_name):
    """生成字段说明文档"""
    readme_path = os.path.join(output_dir, f"{base_name}_fields_description.txt")
    
    try:
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write("多情景降水数据渔网字段说明\n")
            f.write("=" * 50 + "\n\n")
            
            f.write("原始字段:\n")
            original_fields = ['grid_id', 'row_index', 'col_index', 'pixel_value', 
                             'center_x', 'center_y', 'x_min', 'x_max', 'y_min', 'y_max', 'area_deg2']
            for field in original_fields:
                if field in fishnet_gdf.columns:
                    f.write(f"- {field}: 原始渔网字段\n")
            
            f.write("\n降水数据字段:\n")
            f.write("- historical_precip: 历史极端降水值 (1971-2020年平均, mm)\n")
            f.write("- ssp126_precip: SSP1-2.6情景2100年极端降水值 (mm)\n")
            f.write("- ssp245_precip: SSP2-4.5情景2100年极端降水值 (mm)\n")
            f.write("- ssp370_precip: SSP3-7.0情景2100年极端降水值 (mm)\n")
            f.write("- ssp585_precip: SSP5-8.5情景2100年极端降水值 (mm)\n")
            
            f.write(f"\n数据说明:\n")
            f.write(f"- 数据类型: RX1-day (年最大日降水量)\n")
            f.write(f"- 历史时期: 1971-2020年\n")
            f.write(f"- 未来时期: 2100年\n")
            f.write(f"- 情景说明:\n")
            f.write(f"  * SSP1-2.6: 低排放情景\n")
            f.write(f"  * SSP2-4.5: 中等排放情景\n")
            f.write(f"  * SSP3-7.0: 高排放情景\n")
            f.write(f"  * SSP5-8.5: 极高排放情景\n")
            
            f.write(f"\n数据统计:\n")
            f.write(f"- 总网格数: {len(fishnet_gdf)}\n")
            f.write(f"- 坐标系: {fishnet_gdf.crs}\n")
            
            # 添加各情景的统计信息
            precip_fields = ['historical_precip', 'ssp126_precip', 'ssp245_precip', 'ssp370_precip', 'ssp585_precip']
            for field in precip_fields:
                if field in fishnet_gdf.columns:
                    valid_data = fishnet_gdf[field].dropna()
                    if len(valid_data) > 0:
                        f.write(f"\n{field} 统计:\n")
                        f.write(f"  有效值数量: {len(valid_data)}\n")
                        f.write(f"  数据范围: {valid_data.min():.2f} - {valid_data.max():.2f} mm\n")
                        f.write(f"  平均值: {valid_data.mean():.2f} mm\n")
                        f.write(f"  标准差: {valid_data.std():.2f} mm\n")
            
        print(f"✓ 字段说明已保存: {readme_path}")
    except Exception as e:
        print(f"✗ 字段说明保存失败: {e}")

def main():
    """主函数"""
    print("开始多情景降水数据提取...")
    
    # 文件路径设置
    fishnet_file = r"Z:\yuan\paper3_new02\shp\basins_fishnet\valid_pixels_fishnet.shp"
    output_dir = r"Z:\yuan\paper3_new02\EPEs_plot\EPEs_his_map8"
    
    # 数据文件配置
    data_config = {
        'historical_precip': r"Z:\yuan\paper3_new02\EPEs_clip\historical_mean\RX1-day_1971_2020_mean.tif",
        'ssp126_precip': r"Z:\yuan\paper3_new02\EPEs_clip\ssp_mme_jz_5yr\ssp126\Mean\RX1-day\RX1-day_2100.tif",
        'ssp245_precip': r"Z:\yuan\paper3_new02\EPEs_clip\ssp_mme_jz_5yr\ssp245\Mean\RX1-day\RX1-day_2100.tif",
        'ssp370_precip': r"Z:\yuan\paper3_new02\EPEs_clip\ssp_mme_jz_5yr\ssp370\Mean\RX1-day\RX1-day_2100.tif",
        'ssp585_precip': r"Z:\yuan\paper3_new02\EPEs_clip\ssp_mme_jz_5yr\ssp585\Mean\RX1-day\RX1-day_2100.tif"
    }
    
    # 检查并创建输出目录
    check_and_create_output_dir(output_dir)
    
    # 读取渔网
    print("\n1. 读取渔网文件...")
    fishnet_gdf = read_fishnet_shapefile(fishnet_file)
    if fishnet_gdf is None:
        print("渔网文件读取失败，程序退出")
        return
    
    # 创建渔网副本，避免修改原始数据
    enhanced_fishnet = fishnet_gdf.copy()
    
    # 提取各情景降水数据
    step = 2
    for field_name, file_path in data_config.items():
        print(f"\n{step}. 提取 {field_name} 数据...")
        enhanced_fishnet = extract_raster_values_to_fishnet(
            enhanced_fishnet, file_path, field_name
        )
        step += 1
    
    # 导出结果
    print(f"\n{step}. 导出多情景降水数据...")
    export_fishnet_data(enhanced_fishnet, output_dir, "multi_scenario_precipitation")
    
    # 显示处理结果摘要
    print(f"\n✓ 处理完成！")
    print(f"✓ 原始渔网保持不变")
    print(f"✓ 增强渔网已保存到: {output_dir}")
    print(f"✓ 新增字段:")
    for field_name in data_config.keys():
        print(f"  - {field_name}")
    
    # 显示各情景数据统计
    print(f"\n数据统计摘要:")
    for field_name in data_config.keys():
        if field_name in enhanced_fishnet.columns:
            valid_data = enhanced_fishnet[field_name].dropna()
            if len(valid_data) > 0:
                print(f"  {field_name}: {len(valid_data)} 个有效值, "
                      f"范围 {valid_data.min():.1f}-{valid_data.max():.1f} mm, "
                      f"平均 {valid_data.mean():.1f} mm")

if __name__ == "__main__":
    main()
