#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
通用数据提取和分级工具
可适应各种不同数据类型的提取和自定义分级功能
"""

import os
import numpy as np
import rasterio
import geopandas as gpd
import pandas as pd
from rasterio.sample import sample_gen
import warnings
warnings.filterwarnings('ignore')

class UniversalDataExtractor:
    """通用数据提取和分级器"""
    
    def __init__(self, config):
        """
        初始化配置
        config: 配置字典，包含所有必要参数
        """
        self.config = config
        self.validate_config()
    
    def validate_config(self):
        """验证配置参数"""
        required_keys = ['fishnet_file', 'output_dir', 'data_sources']
        for key in required_keys:
            if key not in self.config:
                raise ValueError(f"配置中缺少必需参数: {key}")
        
        # 验证数据源配置
        for i, source in enumerate(self.config['data_sources']):
            required_source_keys = ['file_path', 'field_name']
            for key in required_source_keys:
                if key not in source:
                    raise ValueError(f"数据源 {i} 缺少必需参数: {key}")
    
    def create_classification_function(self, class_config):
        """
        根据配置创建分级函数
        class_config: 分级配置
        {
            'method': 'ranges',  # 'ranges', 'quantiles', 'equal_interval'
            'num_classes': 5,
            'ranges': [(0, 25), (25, 50), (50, 100), (100, 150), (150, float('inf'))],
            'labels': ['极小', '小', '中等', '大', '极大'],
            'include_zero': False  # 是否将0值单独分类
        }
        """
        method = class_config.get('method', 'ranges')
        num_classes = class_config.get('num_classes', 5)
        include_zero = class_config.get('include_zero', False)
        
        if method == 'ranges':
            ranges = class_config['ranges']
            labels = class_config.get('labels', [f'级别{i+1}' for i in range(len(ranges))])
            
            def classify_by_ranges(value):
                if pd.isna(value) or np.isnan(value):
                    return np.nan
                
                if include_zero and value == 0:
                    return 0  # 特殊处理0值
                
                for i, (min_val, max_val) in enumerate(ranges):
                    if min_val <= value < max_val or (i == len(ranges)-1 and value >= min_val):
                        return i + 1
                return np.nan
            
            return classify_by_ranges, labels
        
        elif method == 'quantiles':
            def classify_by_quantiles(data_series):
                """基于分位数的分级（需要整个数据系列）"""
                valid_data = data_series.dropna()
                if include_zero:
                    valid_data = valid_data[valid_data != 0]
                
                if len(valid_data) == 0:
                    return lambda x: np.nan
                
                quantiles = np.linspace(0, 1, num_classes + 1)
                thresholds = valid_data.quantile(quantiles).values
                labels = class_config.get('labels', [f'Q{i+1}' for i in range(num_classes)])
                
                def classify_value(value):
                    if pd.isna(value) or np.isnan(value):
                        return np.nan
                    if include_zero and value == 0:
                        return 0
                    
                    for i in range(len(thresholds) - 1):
                        if thresholds[i] <= value < thresholds[i + 1] or (i == len(thresholds) - 2 and value >= thresholds[i]):
                            return i + 1
                    return np.nan
                
                return classify_value
            
            return classify_by_quantiles, class_config.get('labels', [f'Q{i+1}' for i in range(num_classes)])
        
        elif method == 'equal_interval':
            def classify_by_equal_interval(data_series):
                """基于等间距的分级"""
                valid_data = data_series.dropna()
                if include_zero:
                    valid_data = valid_data[valid_data != 0]
                
                if len(valid_data) == 0:
                    return lambda x: np.nan
                
                min_val, max_val = valid_data.min(), valid_data.max()
                interval = (max_val - min_val) / num_classes
                labels = class_config.get('labels', [f'区间{i+1}' for i in range(num_classes)])
                
                def classify_value(value):
                    if pd.isna(value) or np.isnan(value):
                        return np.nan
                    if include_zero and value == 0:
                        return 0
                    
                    class_idx = int((value - min_val) / interval)
                    return min(max(class_idx + 1, 1), num_classes)
                
                return classify_value
            
            return classify_by_equal_interval, class_config.get('labels', [f'区间{i+1}' for i in range(num_classes)])
    
    def get_class_description(self, class_value, labels, ranges=None):
        """获取分级描述"""
        if pd.isna(class_value):
            return "无数据"
        if class_value == 0:
            return "零值"
        
        class_idx = int(class_value) - 1
        if 0 <= class_idx < len(labels):
            description = labels[class_idx]
            if ranges and class_idx < len(ranges):
                min_val, max_val = ranges[class_idx]
                if max_val == float('inf'):
                    description += f" (≥{min_val})"
                else:
                    description += f" ({min_val}-{max_val})"
            return description
        return "未知"
    
    def check_and_create_output_dir(self):
        """检查并创建输出目录"""
        output_dir = self.config['output_dir']
        if not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            print(f"创建输出目录: {output_dir}")
        else:
            print(f"输出目录已存在: {output_dir}")
    
    def read_fishnet_shapefile(self):
        """读取渔网shapefile"""
        fishnet_file = self.config['fishnet_file']
        try:
            fishnet_gdf = gpd.read_file(fishnet_file)
            print(f"成功读取渔网文件: {os.path.basename(fishnet_file)}")
            print(f"  - 网格数量: {len(fishnet_gdf)}")
            print(f"  - CRS: {fishnet_gdf.crs}")
            print(f"  - 现有字段: {list(fishnet_gdf.columns)}")
            return fishnet_gdf
        except Exception as e:
            print(f"读取渔网文件失败 {fishnet_file}: {e}")
            return None
    
    def read_raster_data(self, file_path):
        """读取栅格数据"""
        try:
            with rasterio.open(file_path) as src:
                data = src.read(1)
                transform = src.transform
                crs = src.crs
                bounds = src.bounds
                nodata = src.nodata
                
            print(f"成功读取栅格: {os.path.basename(file_path)}")
            print(f"  - 数据形状: {data.shape}")
            print(f"  - CRS: {crs}")
            print(f"  - 数据范围: {np.nanmin(data):.2f} - {np.nanmax(data):.2f}")
            print(f"  - NoData值: {nodata}")
            
            return data, transform, crs, bounds, nodata
        except Exception as e:
            print(f"读取栅格文件失败 {file_path}: {e}")
            return None, None, None, None, None
    
    def extract_raster_values_to_fishnet(self, fishnet_gdf, raster_file, field_name, class_config=None):
        """从栅格提取值到渔网并进行分级"""
        print(f"\n提取 {field_name} 数据...")
        
        # 读取栅格数据
        data, transform, crs, bounds, nodata = self.read_raster_data(raster_file)
        if data is None:
            print(f"无法读取栅格文件: {raster_file}")
            return fishnet_gdf
        
        # 检查坐标系是否一致
        if fishnet_gdf.crs != crs:
            print(f"  坐标系不一致，将渔网从 {fishnet_gdf.crs} 转换为 {crs}")
            fishnet_gdf_proj = fishnet_gdf.to_crs(crs)
        else:
            fishnet_gdf_proj = fishnet_gdf.copy()
        
        # 获取网格中心点坐标
        centroids = fishnet_gdf_proj.geometry.centroid
        coords = [(point.x, point.y) for point in centroids]
        
        print(f"  提取 {len(coords)} 个点的栅格值...")
        
        # 使用rasterio的sample函数提取值
        try:
            with rasterio.open(raster_file) as src:
                sampled_values = list(sample_gen(src, coords))
                values = [val[0] if val.size > 0 else np.nan for val in sampled_values]
            
            # 处理NoData值
            if nodata is not None:
                values = [np.nan if val == nodata else val for val in values]
            
            # 添加到原始GeoDataFrame
            fishnet_gdf[field_name] = values
            
            # 统计信息
            valid_values = [v for v in values if not np.isnan(v)]
            print(f"  有效值数量: {len(valid_values)}/{len(values)}")
            if valid_values:
                print(f"  数据范围: {min(valid_values):.2f} - {max(valid_values):.2f}")
                print(f"  平均值: {np.mean(valid_values):.2f}")
            
            # 进行分级（如果提供了分级配置）
            if class_config:
                self.add_classification(fishnet_gdf, field_name, class_config)
            
        except Exception as e:
            print(f"  提取栅格值时出错: {e}")
            fishnet_gdf[field_name] = np.nan
        
        return fishnet_gdf
    
    def add_classification(self, fishnet_gdf, field_name, class_config):
        """为字段添加分级"""
        print(f"  为 {field_name} 添加分级...")
        
        class_field = f"{field_name}_class"
        
        # 创建分级函数
        classify_func, labels = self.create_classification_function(class_config)
        
        # 对于需要整个数据系列的方法
        if class_config.get('method') in ['quantiles', 'equal_interval']:
            classify_func = classify_func(fishnet_gdf[field_name])
        
        # 应用分级函数
        fishnet_gdf[class_field] = fishnet_gdf[field_name].apply(classify_func)
        
        # 统计各等级数量
        class_counts = fishnet_gdf[class_field].value_counts().sort_index()
        print(f"  分级统计:")
        
        ranges = class_config.get('ranges', None)
        for class_val, count in class_counts.items():
            if not pd.isna(class_val):
                desc = self.get_class_description(class_val, labels, ranges)
                print(f"    等级 {int(class_val)}: {count} 个网格 ({desc})")
        
        # 统计无效值
        nan_count = fishnet_gdf[class_field].isna().sum()
        if nan_count > 0:
            print(f"    无效值: {nan_count} 个网格")
    
    def calculate_data_changes(self, fishnet_gdf, field1, field2, change_config=None):
        """计算两个字段之间的变化"""
        print(f"\n计算 {field1} 和 {field2} 之间的变化...")
        
        # 计算绝对变化量
        change_field = f"{field2}_minus_{field1}"
        fishnet_gdf[change_field] = fishnet_gdf[field2] - fishnet_gdf[field1]
        
        # 计算相对变化百分比
        change_pct_field = f"{field2}_pct_change"
        fishnet_gdf[change_pct_field] = ((fishnet_gdf[field2] - fishnet_gdf[field1]) / 
                                        fishnet_gdf[field1] * 100)
        
        # 如果两个字段都有分级，计算等级变化
        class1_field = f"{field1}_class"
        class2_field = f"{field2}_class"
        
        if class1_field in fishnet_gdf.columns and class2_field in fishnet_gdf.columns:
            class_change_field = f"{field2}_class_change"
            fishnet_gdf[class_change_field] = fishnet_gdf[class2_field] - fishnet_gdf[class1_field]
            print(f"  添加等级变化字段: {class_change_field}")
        
        # 如果提供了变化分级配置，对变化量进行分级
        if change_config:
            self.add_classification(fishnet_gdf, change_field, change_config)
        
        # 统计变化量
        valid_changes = fishnet_gdf[change_field].dropna()
        valid_pct_changes = fishnet_gdf[change_pct_field].dropna()
        
        if len(valid_changes) > 0:
            print(f"  绝对变化量统计:")
            print(f"    平均变化: {valid_changes.mean():.2f}")
            print(f"    变化范围: {valid_changes.min():.2f} - {valid_changes.max():.2f}")
            print(f"    标准差: {valid_changes.std():.2f}")
        
        if len(valid_pct_changes) > 0:
            print(f"  相对变化量统计:")
            print(f"    平均变化: {valid_pct_changes.mean():.2f}%")
            print(f"    变化范围: {valid_pct_changes.min():.2f}% - {valid_pct_changes.max():.2f}%")
            print(f"    标准差: {valid_pct_changes.std():.2f}%")
        
        return fishnet_gdf

    def export_enhanced_fishnet(self, fishnet_gdf, base_name="enhanced_fishnet"):
        """导出增强的渔网数据"""
        output_dir = self.config['output_dir']
        print(f"\n导出增强渔网数据到: {output_dir}")

        # 导出Shapefile
        shapefile_path = os.path.join(output_dir, f"{base_name}.shp")
        try:
            fishnet_gdf.to_file(shapefile_path, encoding='utf-8')
            print(f"✓ Shapefile已保存: {shapefile_path}")
        except Exception as e:
            print(f"✗ Shapefile保存失败: {e}")

        # 导出GeoJSON
        geojson_path = os.path.join(output_dir, f"{base_name}.geojson")
        try:
            fishnet_gdf.to_file(geojson_path, driver='GeoJSON', encoding='utf-8')
            print(f"✓ GeoJSON已保存: {geojson_path}")
        except Exception as e:
            print(f"✗ GeoJSON保存失败: {e}")

        # 导出CSV
        csv_path = os.path.join(output_dir, f"{base_name}_data.csv")
        try:
            data_df = fishnet_gdf.drop('geometry', axis=1)
            data_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
            print(f"✓ CSV数据已保存: {csv_path}")
        except Exception as e:
            print(f"✗ CSV保存失败: {e}")

        # 导出Excel
        excel_path = os.path.join(output_dir, f"{base_name}_data.xlsx")
        try:
            data_df = fishnet_gdf.drop('geometry', axis=1)
            data_df.to_excel(excel_path, index=False, engine='openpyxl')
            print(f"✓ Excel数据已保存: {excel_path}")
        except Exception as e:
            print(f"✗ Excel保存失败: {e}")

        # 生成字段说明文件
        self.generate_field_description(fishnet_gdf, base_name)

    def generate_field_description(self, fishnet_gdf, base_name):
        """生成字段说明文档"""
        output_dir = self.config['output_dir']
        readme_path = os.path.join(output_dir, f"{base_name}_fields_description.txt")

        try:
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write("通用数据提取和分级结果字段说明\n")
                f.write("=" * 60 + "\n\n")

                f.write("原始字段:\n")
                original_fields = ['grid_id', 'row_index', 'col_index', 'pixel_value',
                                 'center_x', 'center_y', 'x_min', 'x_max', 'y_min', 'y_max', 'area_deg2']
                for field in original_fields:
                    if field in fishnet_gdf.columns:
                        f.write(f"- {field}: 原始渔网字段\n")

                f.write("\n数据字段:\n")
                for col in fishnet_gdf.columns:
                    if col.endswith('_class') and 'change' not in col:
                        f.write(f"- {col}: 数据分级字段\n")
                    elif 'minus' in col.lower():
                        f.write(f"- {col}: 绝对变化量\n")
                    elif 'pct_change' in col.lower():
                        f.write(f"- {col}: 相对变化百分比 (%)\n")
                    elif 'class_change' in col.lower():
                        f.write(f"- {col}: 等级变化 (级数差)\n")
                    elif col not in original_fields and col != 'geometry':
                        f.write(f"- {col}: 提取的数据值\n")

                # 添加分级配置信息
                f.write(f"\n分级配置:\n")
                for source in self.config['data_sources']:
                    if 'classification' in source:
                        class_config = source['classification']
                        field_name = source['field_name']
                        f.write(f"- {field_name} 分级方法: {class_config.get('method', 'ranges')}\n")
                        if 'ranges' in class_config:
                            f.write(f"  分级范围: {class_config['ranges']}\n")
                        if 'labels' in class_config:
                            f.write(f"  等级标签: {class_config['labels']}\n")

                f.write(f"\n数据统计:\n")
                f.write(f"- 总网格数: {len(fishnet_gdf)}\n")
                f.write(f"- 坐标系: {fishnet_gdf.crs}\n")

            print(f"✓ 字段说明已保存: {readme_path}")
        except Exception as e:
            print(f"✗ 字段说明保存失败: {e}")

    def process_all_data(self):
        """处理所有数据的主函数"""
        print("开始通用数据提取和分级处理...")

        # 检查并创建输出目录
        self.check_and_create_output_dir()

        # 读取渔网
        print("\n1. 读取渔网文件...")
        fishnet_gdf = self.read_fishnet_shapefile()
        if fishnet_gdf is None:
            print("渔网文件读取失败，程序退出")
            return None

        # 创建渔网副本
        enhanced_fishnet = fishnet_gdf.copy()

        # 处理每个数据源
        step = 2
        for i, source in enumerate(self.config['data_sources']):
            print(f"\n{step}. 处理数据源 {i+1}: {source['field_name']}...")

            enhanced_fishnet = self.extract_raster_values_to_fishnet(
                enhanced_fishnet,
                source['file_path'],
                source['field_name'],
                source.get('classification', None)
            )
            step += 1

        # 处理变化分析（如果配置了）
        if 'change_analysis' in self.config:
            for change in self.config['change_analysis']:
                print(f"\n{step}. 计算变化分析: {change['field1']} vs {change['field2']}...")
                enhanced_fishnet = self.calculate_data_changes(
                    enhanced_fishnet,
                    change['field1'],
                    change['field2'],
                    change.get('change_classification', None)
                )
                step += 1

        # 导出结果
        print(f"\n{step}. 导出处理结果...")
        output_name = self.config.get('output_name', 'enhanced_fishnet')
        self.export_enhanced_fishnet(enhanced_fishnet, output_name)

        print(f"\n✓ 处理完成！")
        print(f"✓ 处理了 {len(self.config['data_sources'])} 个数据源")
        print(f"✓ 生成了 {len([col for col in enhanced_fishnet.columns if col != 'geometry'])} 个字段")
        print(f"✓ 结果已保存到: {self.config['output_dir']}")

        return enhanced_fishnet


def create_sample_config():
    """创建示例配置"""
    config = {
        # 基本文件路径
        'fishnet_file': r"Z:\yuan\paper3_new02\shp\basins_fishnet\valid_pixels_fishnet.shp",
        'output_dir': r"Z:\yuan\paper3_new02\EPEs_plot\universal_output",
        'output_name': 'multi_data_analysis',

        # 数据源配置
        'data_sources': [
            {
                'file_path': r"Z:\yuan\paper3_new02\EPEs_clip\historical_mean\RX1-day_1971_2020_mean.tif",
                'field_name': 'historical_precip',
                'classification': {
                    'method': 'ranges',
                    'ranges': [(0, 25), (25, 50), (50, 100), (100, 150), (150, float('inf'))],
                    'labels': ['极小', '小', '中等', '大', '极大'],
                    'include_zero': False
                }
            },
            {
                'file_path': r"Z:\yuan\paper3_new02\EPEs_clip\ssp_mme_jz_5yr\ssp245\Mean\RX1-day\RX1-day_2100.tif",
                'field_name': 'future_precip',
                'classification': {
                    'method': 'ranges',
                    'ranges': [(0, 25), (25, 50), (50, 100), (100, 150), (150, float('inf'))],
                    'labels': ['极小', '小', '中等', '大', '极大'],
                    'include_zero': False
                }
            }
        ],

        # 变化分析配置
        'change_analysis': [
            {
                'field1': 'historical_precip',
                'field2': 'future_precip',
                'change_classification': {
                    'method': 'ranges',
                    'ranges': [(-float('inf'), -20), (-20, -5), (-5, 5), (5, 20), (20, float('inf'))],
                    'labels': ['大幅减少', '轻微减少', '基本不变', '轻微增加', '大幅增加'],
                    'include_zero': True
                }
            }
        ]
    }
    return config


def create_custom_config():
    """创建自定义配置模板 - 用户可以修改此函数来适应不同需求"""
    config = {
        # ========== 基本配置 ==========
        'fishnet_file': r"你的渔网文件路径.shp",
        'output_dir': r"输出目录路径",
        'output_name': '输出文件名前缀',

        # ========== 数据源配置 ==========
        'data_sources': [
            # 数据源1
            {
                'file_path': r"数据文件1路径.tif",
                'field_name': '字段名1',
                'classification': {
                    'method': 'ranges',  # 'ranges', 'quantiles', 'equal_interval'
                    'num_classes': 5,    # 分级数量
                    'ranges': [(0, 10), (10, 20), (20, 50), (50, 100), (100, float('inf'))],
                    'labels': ['很低', '低', '中', '高', '很高'],
                    'include_zero': False  # 是否单独处理0值
                }
            },
            # 数据源2 - 使用分位数分级
            {
                'file_path': r"数据文件2路径.tif",
                'field_name': '字段名2',
                'classification': {
                    'method': 'quantiles',
                    'num_classes': 4,
                    'labels': ['第1四分位', '第2四分位', '第3四分位', '第4四分位'],
                    'include_zero': False
                }
            },
            # 数据源3 - 不进行分级
            {
                'file_path': r"数据文件3路径.tif",
                'field_name': '字段名3'
                # 不包含classification则不进行分级
            }
        ],

        # ========== 变化分析配置（可选） ==========
        'change_analysis': [
            {
                'field1': '字段名1',  # 基准字段
                'field2': '字段名2',  # 对比字段
                'change_classification': {  # 对变化量进行分级（可选）
                    'method': 'ranges',
                    'ranges': [(-float('inf'), -10), (-10, -2), (-2, 2), (2, 10), (10, float('inf'))],
                    'labels': ['大幅下降', '轻微下降', '基本不变', '轻微上升', '大幅上升'],
                    'include_zero': True
                }
            }
        ]
    }
    return config


def main():
    """主函数 - 使用示例配置"""
    # 创建示例配置
    config = create_sample_config()

    # 如果要使用自定义配置，请修改create_custom_config()函数并取消下面的注释
    # config = create_custom_config()

    # 创建处理器并运行
    extractor = UniversalDataExtractor(config)
    result = extractor.process_all_data()

    return result


if __name__ == "__main__":
    main()
