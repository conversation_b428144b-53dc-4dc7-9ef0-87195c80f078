#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于有效像元创建方格渔网工具
读取有效像元标准文件，生成对应的方格渔网shapefile
"""

import os
import numpy as np
import rasterio
import geopandas as gpd
from shapely.geometry import box
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

def check_and_create_output_dir(output_path):
    """检查并创建输出目录"""
    if not os.path.exists(output_path):
        os.makedirs(output_path, exist_ok=True)
        print(f"创建输出目录: {output_path}")
    else:
        print(f"输出目录已存在: {output_path}")

def read_valid_pixels_raster(file_path):
    """读取有效像元栅格数据"""
    try:
        with rasterio.open(file_path) as src:
            data = src.read(1)
            transform = src.transform
            crs = src.crs
            bounds = src.bounds
            nodata = src.nodata
            width = src.width
            height = src.height
            
        print(f"成功读取有效像元文件: {os.path.basename(file_path)}")
        print(f"  - 数据形状: {data.shape}")
        print(f"  - 宽度×高度: {width} × {height}")
        print(f"  - CRS: {crs}")
        print(f"  - 边界范围: {bounds}")
        print(f"  - NoData值: {nodata}")
        print(f"  - 数据类型: {data.dtype}")
        
        # 统计有效像元
        if nodata is not None:
            valid_mask = (data != nodata) & (~np.isnan(data))
        else:
            valid_mask = ~np.isnan(data)
            
        valid_count = np.sum(valid_mask)
        total_count = data.size
        
        print(f"  - 总像元数: {total_count}")
        print(f"  - 有效像元数: {valid_count}")
        print(f"  - 有效比例: {valid_count/total_count*100:.2f}%")
        
        return data, transform, crs, bounds, nodata, valid_mask
        
    except Exception as e:
        print(f"读取文件失败 {file_path}: {e}")
        return None, None, None, None, None, None

def create_fishnet_from_valid_pixels(data, transform, crs, bounds, valid_mask):
    """根据有效像元创建方格渔网"""
    print("\n开始创建方格渔网...")
    
    # 获取栅格的像元大小
    pixel_width = abs(transform[0])
    pixel_height = abs(transform[4])
    
    print(f"像元大小: {pixel_width:.6f} × {pixel_height:.6f}")
    
    # 获取有效像元的行列索引
    rows, cols = np.where(valid_mask)
    
    print(f"找到 {len(rows)} 个有效像元")
    
    # 存储网格信息
    polygons = []
    grid_data = []
    
    for i, (row, col) in enumerate(zip(rows, cols)):
        # 计算像元的地理坐标
        # 注意：transform[2]是x偏移，transform[5]是y偏移
        x_min = transform[2] + col * pixel_width
        x_max = x_min + pixel_width
        y_max = transform[5] - row * pixel_height  # 注意y坐标是从上到下递减的
        y_min = y_max - pixel_height
        
        # 创建多边形（矩形网格）
        polygon = box(x_min, y_min, x_max, y_max)
        polygons.append(polygon)
        
        # 存储网格属性信息
        grid_info = {
            'grid_id': i + 1,  # 从1开始编号
            'row_index': int(row),
            'col_index': int(col),
            'pixel_value': float(data[row, col]),
            'center_x': (x_min + x_max) / 2,
            'center_y': (y_min + y_max) / 2,
            'x_min': x_min,
            'x_max': x_max,
            'y_min': y_min,
            'y_max': y_max,
            'area_deg2': pixel_width * pixel_height
        }
        grid_data.append(grid_info)
        
        # 进度显示
        if (i + 1) % 1000 == 0 or (i + 1) == len(rows):
            print(f"  处理进度: {i + 1}/{len(rows)} ({(i + 1)/len(rows)*100:.1f}%)")
    
    # 创建GeoDataFrame
    gdf = gpd.GeoDataFrame(grid_data, geometry=polygons, crs=crs)
    
    print(f"成功创建 {len(gdf)} 个网格单元")
    
    # 显示统计信息
    print(f"\n网格统计信息:")
    print(f"  - 网格总数: {len(gdf)}")
    print(f"  - 像元值范围: {gdf['pixel_value'].min():.2f} - {gdf['pixel_value'].max():.2f}")
    print(f"  - 行索引范围: {gdf['row_index'].min()} - {gdf['row_index'].max()}")
    print(f"  - 列索引范围: {gdf['col_index'].min()} - {gdf['col_index'].max()}")
    print(f"  - X坐标范围: {gdf['center_x'].min():.6f} - {gdf['center_x'].max():.6f}")
    print(f"  - Y坐标范围: {gdf['center_y'].min():.6f} - {gdf['center_y'].max():.6f}")
    
    return gdf

def export_fishnet_data(fishnet_gdf, output_dir):
    """导出方格渔网数据"""
    print(f"\n开始导出数据到: {output_dir}")
    
    # 导出shapefile
    shapefile_path = os.path.join(output_dir, "valid_pixels_fishnet.shp")
    try:
        fishnet_gdf.to_file(shapefile_path, encoding='utf-8')
        print(f"✓ Shapefile已保存: {shapefile_path}")
    except Exception as e:
        print(f"✗ Shapefile保存失败: {e}")
    
    # 导出GeoJSON
    geojson_path = os.path.join(output_dir, "valid_pixels_fishnet.geojson")
    try:
        fishnet_gdf.to_file(geojson_path, driver='GeoJSON', encoding='utf-8')
        print(f"✓ GeoJSON已保存: {geojson_path}")
    except Exception as e:
        print(f"✗ GeoJSON保存失败: {e}")
    
    # 导出CSV（不包含几何信息）
    csv_path = os.path.join(output_dir, "valid_pixels_data.csv")
    try:
        # 移除geometry列
        data_df = fishnet_gdf.drop('geometry', axis=1)
        data_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"✓ CSV数据已保存: {csv_path}")
    except Exception as e:
        print(f"✗ CSV保存失败: {e}")
    
    # 导出Excel
    excel_path = os.path.join(output_dir, "valid_pixels_data.xlsx")
    try:
        data_df = fishnet_gdf.drop('geometry', axis=1)
        data_df.to_excel(excel_path, index=False, engine='openpyxl')
        print(f"✓ Excel数据已保存: {excel_path}")
    except Exception as e:
        print(f"✗ Excel保存失败: {e}")
    
    # 生成数据说明文件
    readme_path = os.path.join(output_dir, "README.txt")
    try:
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write("有效像元方格渔网数据说明\n")
            f.write("=" * 50 + "\n\n")
            f.write("字段说明:\n")
            f.write("- grid_id: 网格唯一标识符（从1开始）\n")
            f.write("- row_index: 原始栅格中的行索引\n")
            f.write("- col_index: 原始栅格中的列索引\n")
            f.write("- pixel_value: 原始像元值\n")
            f.write("- center_x: 网格中心点X坐标\n")
            f.write("- center_y: 网格中心点Y坐标\n")
            f.write("- x_min, x_max: 网格X坐标范围\n")
            f.write("- y_min, y_max: 网格Y坐标范围\n")
            f.write("- area_deg2: 网格面积（平方度）\n\n")
            f.write("文件说明:\n")
            f.write("- valid_pixels_fishnet.shp: Shapefile格式的方格渔网\n")
            f.write("- valid_pixels_fishnet.geojson: GeoJSON格式的方格渔网\n")
            f.write("- valid_pixels_data.csv: CSV格式的属性数据\n")
            f.write("- valid_pixels_data.xlsx: Excel格式的属性数据\n")
        print(f"✓ 说明文件已保存: {readme_path}")
    except Exception as e:
        print(f"✗ 说明文件保存失败: {e}")

def main():
    """主函数"""
    print("开始处理有效像元方格渔网生成...")
    
    # 文件路径设置
    valid_pixels_file = r"Z:\yuan\paper3_new02\shp\valid_pixels\updated_valid_pixels.tif"
    output_dir = r"Z:\yuan\paper3_new02\shp\basins_fishnet"
    
    # 检查并创建输出目录
    check_and_create_output_dir(output_dir)
    
    # 读取有效像元数据
    print("\n1. 读取有效像元标准文件...")
    data, transform, crs, bounds, nodata, valid_mask = read_valid_pixels_raster(valid_pixels_file)
    
    if data is None:
        print("有效像元文件读取失败，程序退出")
        return
    
    if np.sum(valid_mask) == 0:
        print("未找到有效像元，程序退出")
        return
    
    # 创建方格渔网
    print("\n2. 创建方格渔网...")
    fishnet_gdf = create_fishnet_from_valid_pixels(data, transform, crs, bounds, valid_mask)
    
    # 导出数据
    print("\n3. 导出方格渔网数据...")
    export_fishnet_data(fishnet_gdf, output_dir)
    
    print(f"\n✓ 处理完成！所有文件已保存到: {output_dir}")
    print(f"✓ 共生成 {len(fishnet_gdf)} 个网格单元")

if __name__ == "__main__":
    main()
