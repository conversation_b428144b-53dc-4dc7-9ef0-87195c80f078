#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
历史极端降水栅格数据可视化
读取研究区边界和历史极端降水栅格，制作美观的地图
"""

import geopandas as gpd
import rasterio
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from rasterio.plot import show
from matplotlib.colors import LinearSegmentedColormap
import warnings
warnings.filterwarnings('ignore')

def create_custom_colormap():
    """创建自定义颜色映射"""
    colors = ['#ffffff', '#e6f3ff', '#b3d9ff', '#80bfff', '#4da6ff', 
              '#1a8cff', '#0066cc', '#004d99', '#003366', '#001a33']
    n_bins = 256
    cmap = LinearSegmentedColormap.from_list('precipitation', colors, N=n_bins)
    return cmap

def add_north_arrow(ax, x=0.95, y=0.95, size=0.05):
    """添加指北针"""
    # 指北针的位置和大小
    arrow_x = x
    arrow_y = y
    arrow_size = size

    # 绘制指北针箭头
    arrow = patches.FancyArrowPatch((arrow_x, arrow_y - arrow_size/2),
                                   (arrow_x, arrow_y + arrow_size/2),
                                   arrowstyle='->',
                                   mutation_scale=20,
                                   color='black',
                                   transform=ax.transAxes)
    ax.add_patch(arrow)

    # 添加"N"标签
    ax.text(arrow_x, arrow_y + arrow_size/2 + 0.02, 'N',
            transform=ax.transAxes, ha='center', va='bottom',
            fontsize=12, fontweight='bold')

def add_scale_bar(ax, length_km=50, x=0.05, y=0.05):
    """添加比例尺"""
    # 获取当前坐标轴的范围
    xlim = ax.get_xlim()
    ylim = ax.get_ylim()

    # 计算度数对应的公里数（粗略估算：1度约等于111公里）
    length_deg = length_km / 111.0

    # 比例尺的起始位置
    x_start = xlim[0] + (xlim[1] - xlim[0]) * x
    y_start = ylim[0] + (ylim[1] - ylim[0]) * y

    # 绘制比例尺线条
    ax.plot([x_start, x_start + length_deg], [y_start, y_start],
            'k-', linewidth=3, solid_capstyle='butt')

    # 添加刻度
    ax.plot([x_start, x_start], [y_start - length_deg*0.02, y_start + length_deg*0.02],
            'k-', linewidth=2)
    ax.plot([x_start + length_deg, x_start + length_deg],
            [y_start - length_deg*0.02, y_start + length_deg*0.02], 'k-', linewidth=2)

    # 添加标签
    ax.text(x_start + length_deg/2, y_start - length_deg*0.08, f'{length_km} km',
            ha='center', va='top', fontsize=10, fontweight='bold',
            bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))

def plot_precipitation_map():
    """主函数：绘制降水地图"""
    
    # 文件路径
    boundary_file = r"Z:\yuan\paper3_new02\shp\basins_lev01-12\basins_lev05_boundary.shp"
    precipitation_file = r"Z:\yuan\paper3_new02\EPEs_clip\historical_mean\RX1-day_1971_2020_mean.tif"
    
    print("正在读取数据文件...")
    
    try:
        # 读取边界数据
        boundary = gpd.read_file(boundary_file)
        print(f"边界数据读取成功，CRS: {boundary.crs}")
        
        # 读取降水栅格数据
        with rasterio.open(precipitation_file) as src:
            precipitation_data = src.read(1)
            precipitation_crs = src.crs
            
        print(f"降水数据读取成功，CRS: {precipitation_crs}")
        print(f"降水数据范围: {np.nanmin(precipitation_data):.2f} - {np.nanmax(precipitation_data):.2f}")
        
        # 确保坐标系一致
        if boundary.crs != precipitation_crs:
            print("正在转换坐标系...")
            boundary = boundary.to_crs(precipitation_crs)
        
        # 创建图形
        _, ax = plt.subplots(1, 1, figsize=(12, 10))
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建自定义颜色映射
        custom_cmap = create_custom_colormap()
        
        # 绘制降水栅格
        with rasterio.open(precipitation_file) as src:
            show(src, ax=ax, cmap=custom_cmap, alpha=0.8)
        
        # 绘制边界
        boundary.plot(ax=ax, facecolor='none', edgecolor='red', linewidth=2, alpha=0.8)
        
        # 设置标题
        ax.set_title('历史极端降水分布图 (RX1-day, 1971-2020年平均)', 
                    fontsize=16, fontweight='bold', pad=20)
        
        # 添加颜色条
        sm = plt.cm.ScalarMappable(cmap=custom_cmap, 
                                  norm=plt.Normalize(vmin=np.nanmin(precipitation_data), 
                                                   vmax=np.nanmax(precipitation_data)))
        sm.set_array([])
        cbar = plt.colorbar(sm, ax=ax, shrink=0.8, aspect=30, pad=0.02)
        cbar.set_label('降水量 (mm)', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        # 添加网格
        ax.grid(True, alpha=0.3, linestyle='--')
        
        # 设置坐标轴标签
        ax.set_xlabel('经度', fontsize=12, fontweight='bold')
        ax.set_ylabel('纬度', fontsize=12, fontweight='bold')
        
        # 美化坐标轴
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        # 添加指北针
        add_north_arrow(ax, x=0.95, y=0.95, size=0.05)
        
        # 添加比例尺
        # 计算合适的比例尺长度（基于数据范围）
        bounds = boundary.total_bounds
        width_km = (bounds[2] - bounds[0]) * 111  # 粗略转换为公里

        if width_km > 1000:
            scale_length = 100  # 100公里
        elif width_km > 500:
            scale_length = 50   # 50公里
        elif width_km > 100:
            scale_length = 20   # 20公里
        else:
            scale_length = 10   # 10公里

        add_scale_bar(ax, length_km=scale_length, x=0.05, y=0.05)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图片
        import os
        output_dir = r"Z:\yuan\paper3_new02\EPEs_plot\EPEs_his_map"
        os.makedirs(output_dir, exist_ok=True)  # 创建目录（如果不存在）
        output_file = os.path.join(output_dir, "precipitation_map.png")
        plt.savefig(output_file, dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        print(f"地图已保存为: {output_file}")
        
        # 显示图片
        plt.show()
        
        # 打印统计信息
        print("\n数据统计信息:")
        print(f"降水数据最小值: {np.nanmin(precipitation_data):.2f} mm")
        print(f"降水数据最大值: {np.nanmax(precipitation_data):.2f} mm")
        print(f"降水数据平均值: {np.nanmean(precipitation_data):.2f} mm")
        print(f"研究区边界范围: {boundary.total_bounds}")
        
    except FileNotFoundError as e:
        print(f"文件未找到: {e}")
    except Exception as e:
        print(f"处理过程中出现错误: {e}")

if __name__ == "__main__":
    plot_precipitation_map()
